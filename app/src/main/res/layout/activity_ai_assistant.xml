<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_background"
    android:fitsSystemWindows="true">

    <!-- 标题栏 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/titleBarContainer"
        android:layout_width="0dp"
        android:layout_height="60dp"
        android:background="?attr/colorPrimary"
        android:elevation="4dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageButton
            android:id="@+id/backButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="8dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_arrow_back"
            android:contentDescription="返回"
            android:tint="?attr/colorOnPrimary"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/titleBar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="AI智能助手"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="?attr/colorOnPrimary"
            android:gravity="center"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="56dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/backButton"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 聊天消息列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/chatRecyclerView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:padding="16dp"
        android:clipToPadding="false"
        android:scrollbarStyle="outsideOverlay"
        android:scrollbars="vertical"
        android:fadeScrollbars="true"
        app:layout_constraintTop_toBottomOf="@id/titleBarContainer"
        app:layout_constraintBottom_toTopOf="@id/inputContainer"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 输入区域 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/inputContainer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="?android:attr/colorBackground"
        android:elevation="4dp"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <EditText
            android:id="@+id/messageInput"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:background="@drawable/input_background"
            android:hint="输入您的问题..."
            android:imeActionLabel="发送"
            android:imeOptions="actionSend"
            android:maxLines="4"
            android:minHeight="48dp"
            android:padding="12dp"
            android:textSize="16sp"
            android:textColor="?android:attr/textColorPrimary"
            android:textColorHint="?android:attr/textColorSecondary"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/voiceButton" />

        <ImageButton
            android:id="@+id/voiceButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="8dp"
            android:background="@drawable/voice_button_background"
            android:src="@drawable/ic_mic"
            android:contentDescription="语音输入"
            android:tint="?attr/colorOnPrimary"
            android:elevation="2dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/sendButton" />

        <ImageButton
            android:id="@+id/sendButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/send_button_background"
            android:src="@drawable/ic_send"
            android:contentDescription="发送"
            android:tint="?attr/colorOnPrimary"
            android:elevation="2dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>