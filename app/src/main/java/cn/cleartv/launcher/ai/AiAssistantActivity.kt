package cn.cleartv.launcher.ai

import android.Manifest
import android.annotation.SuppressLint
import android.content.pm.PackageManager
import android.os.Bundle
import android.view.View
import android.widget.EditText
import android.widget.ImageButton
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import cn.cleartv.launcher.R
import cn.cleartv.launcher.ai.adapter.ChatAdapter
import cn.cleartv.launcher.ai.model.ChatMessage
import cn.cleartv.launcher.ai.model.MessageType
import cn.cleartv.launcher.ai.voice.VoicePlayer
import cn.cleartv.launcher.ai.voice.VoiceRecorder
import cn.cleartv.launcher.enableFullScreen
import cn.cleartv.launcher.log.Log
import cn.cleartv.launcher.showToast
import kotlinx.coroutines.*
import java.io.File

class AiAssistantActivity : AppCompatActivity() {
    companion object {
        private const val REQUEST_RECORD_AUDIO_PERMISSION = 200
    }

    private lateinit var chatRecyclerView: RecyclerView
    private lateinit var chatAdapter: ChatAdapter
    private lateinit var messageInput: EditText
    private lateinit var sendButton: ImageButton
    private lateinit var voiceButton: ImageButton
    private lateinit var titleBar: TextView
    private lateinit var backButton: ImageButton

    private val chatMessages = mutableListOf<ChatMessage>()
    private val aiScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    // 语音相关
    private lateinit var voiceRecorder: VoiceRecorder
    private lateinit var voicePlayer: VoicePlayer
    private var isRecording = false
    private var isPlaying = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.i("AiAssistantActivity onCreate")
        enableFullScreen()
        
        // 隐藏状态栏并设置全屏模式
        window.decorView.systemUiVisibility = (
            View.SYSTEM_UI_FLAG_FULLSCREEN or
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
                View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        )
        
        setContentView(R.layout.activity_ai_assistant)
        
        initViews()
        setupRecyclerView()
        setupInputHandlers()
        initVoiceComponents()

        // 添加欢迎消息
        addWelcomeMessage()
    }
    
    private fun initViews() {
        titleBar = findViewById(R.id.titleBar)
        backButton = findViewById(R.id.backButton)
        chatRecyclerView = findViewById(R.id.chatRecyclerView)
        messageInput = findViewById(R.id.messageInput)
        sendButton = findViewById(R.id.sendButton)
        voiceButton = findViewById(R.id.voiceButton)

        titleBar.text = "AI智能助手"

        backButton.setOnClickListener {
            finish()
        }
    }
    
    private fun setupRecyclerView() {
        chatAdapter = ChatAdapter(chatMessages) { message ->
            // 处理语音播放点击
            playVoiceMessage(message)
        }
        chatRecyclerView.apply {
            layoutManager = LinearLayoutManager(this@AiAssistantActivity).apply {
                stackFromEnd = true
            }
            adapter = chatAdapter
        }
    }
    
    private fun setupInputHandlers() {
        sendButton.setOnClickListener {
            sendMessage()
        }

        voiceButton.setOnClickListener {
            handleVoiceButtonClick()
        }

        messageInput.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == android.view.inputmethod.EditorInfo.IME_ACTION_SEND) {
                sendMessage()
                true
            } else {
                false
            }
        }
    }
    
    private fun addWelcomeMessage() {
        val welcomeMessage = ChatMessage(
            text = "您好！我是Clear Launcher的AI智能助手。\n\n我可以帮助您：\n• 解答设备使用问题\n• 提供应用推荐\n• 协助系统设置\n• 回答技术咨询\n\n请问有什么可以帮助您的吗？",
            isUser = false,
            timestamp = System.currentTimeMillis()
        )
        
        chatMessages.add(welcomeMessage)
        chatAdapter.notifyItemInserted(chatMessages.size - 1)
        scrollToBottom()
    }
    
    private fun sendMessage() {
        val messageText = messageInput.text.toString().trim()
        if (messageText.isEmpty()) {
            showToast("请输入消息")
            return
        }
        
        // 添加用户消息
        val userMessage = ChatMessage(
            text = messageText,
            isUser = true,
            timestamp = System.currentTimeMillis()
        )
        
        chatMessages.add(userMessage)
        chatAdapter.notifyItemInserted(chatMessages.size - 1)
        scrollToBottom()
        
        // 清空输入框
        messageInput.text.clear()
        
        // 模拟AI回复
        simulateAiResponse(messageText)
    }
    
    private fun simulateAiResponse(userMessage: String) {
        // 显示"正在输入"状态
        val typingMessage = ChatMessage(
            text = "正在思考中...",
            isUser = false,
            timestamp = System.currentTimeMillis(),
            isTyping = true
        )
        
        chatMessages.add(typingMessage)
        chatAdapter.notifyItemInserted(chatMessages.size - 1)
        scrollToBottom()
        
        // 模拟AI处理时间
        aiScope.launch {
            delay(1500) // 模拟处理时间
            
            // 移除"正在输入"消息
            chatMessages.removeLastOrNull()
            chatAdapter.notifyItemRemoved(chatMessages.size)
            
            // 生成AI回复
            val aiResponse = generateAiResponse(userMessage)
            val aiMessage = ChatMessage(
                text = aiResponse,
                isUser = false,
                timestamp = System.currentTimeMillis()
            )
            
            chatMessages.add(aiMessage)
            chatAdapter.notifyItemInserted(chatMessages.size - 1)
            scrollToBottom()
        }
    }
    
    private fun generateAiResponse(userMessage: String): String {
        val lowerMessage = userMessage.lowercase()
        
        return when {
            lowerMessage.contains("你好") || lowerMessage.contains("hello") -> {
                "您好！很高兴为您服务。有什么问题需要我帮助解决吗？"
            }
            lowerMessage.contains("时间") -> {
                "当前时间显示在主屏幕上方。您可以通过系统设置调整时间格式和时区。"
            }
            lowerMessage.contains("应用") || lowerMessage.contains("软件") -> {
                "您可以长按主屏幕的应用图标来选择要显示的应用。底部抽屉显示了所有已安装的应用程序。"
            }
            lowerMessage.contains("设置") -> {
                "设置页面可以访问各种系统配置，包括网络、显示、音频等选项。您需要什么特定的设置帮助吗？"
            }
            lowerMessage.contains("网络") || lowerMessage.contains("wifi") -> {
                "网络设置可以在设置页面中找到。支持WiFi和有线网络配置，也提供网络诊断功能。"
            }
            lowerMessage.contains("浏览器") -> {
                "内置的浏览器基于腾讯X5内核，支持现代Web标准。您可以浏览网页、观看视频等。"
            }
            lowerMessage.contains("插件") -> {
                "系统支持多种插件，包括音视频通话、读卡器等功能模块。插件可以动态加载和卸载。"
            }
            lowerMessage.contains("帮助") || lowerMessage.contains("问题") -> {
                "我可以帮助您解决以下问题：\n• 应用管理和使用\n• 系统设置配置\n• 网络连接问题\n• 功能使用指导\n\n请具体描述您遇到的问题。"
            }
            else -> {
                "感谢您的询问。作为Clear Launcher的AI助手，我会尽力帮助您。如果您的问题比较具体，请提供更多详细信息，这样我可以给出更准确的回答。"
            }
        }
    }
    
    private fun scrollToBottom() {
        if (chatMessages.isNotEmpty()) {
            chatRecyclerView.smoothScrollToPosition(chatMessages.size - 1)
        }
    }
    
    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {
        finish()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        aiScope.cancel()
        if (::voiceRecorder.isInitialized) {
            voiceRecorder.release()
        }
        if (::voicePlayer.isInitialized) {
            voicePlayer.release()
        }
    }

    /**
     * 初始化语音组件
     */
    private fun initVoiceComponents() {
        voiceRecorder = VoiceRecorder()
        voicePlayer = VoicePlayer()

        // 设置录音监听器
        voiceRecorder.setOnRecordingStateChangedListener { recording ->
            isRecording = recording
            updateVoiceButtonState()
        }

        voiceRecorder.setOnRecordingCompleteListener { file, duration ->
            if (file != null && file.exists()) {
                // 发送语音消息
                sendVoiceMessage(file, duration)
            } else {
                showToast("录音失败")
            }
        }

        voiceRecorder.setOnErrorListener { error ->
            showToast(error)
            isRecording = false
            updateVoiceButtonState()
        }

        // 设置播放监听器
        voicePlayer.setOnPlaybackStateChangedListener { playing ->
            isPlaying = playing
        }

        voicePlayer.setOnPlaybackCompleteListener {
            isPlaying = false
        }

        voicePlayer.setOnErrorListener { error ->
            showToast(error)
            isPlaying = false
        }
    }

    /**
     * 处理语音按钮点击
     */
    private fun handleVoiceButtonClick() {
        if (isRecording) {
            stopRecording()
        } else {
            startRecording()
        }
    }

    /**
     * 开始录音
     */
    private fun startRecording() {
        // 检查录音权限
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO)
            != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(
                this,
                arrayOf(Manifest.permission.RECORD_AUDIO),
                REQUEST_RECORD_AUDIO_PERMISSION
            )
            return
        }

        // 创建录音文件
        val voiceDir = File(filesDir, "voices")
        if (!voiceDir.exists()) {
            voiceDir.mkdirs()
        }

        val voiceFile = File(voiceDir, "voice_${System.currentTimeMillis()}.pcm")

        if (voiceRecorder.startRecording(voiceFile)) {
            showToast("开始录音...")
        } else {
            showToast("录音启动失败")
        }
    }

    /**
     * 停止录音
     */
    private fun stopRecording() {
        voiceRecorder.stopRecording()
        showToast("录音结束")
    }

    /**
     * 更新语音按钮状态
     */
    private fun updateVoiceButtonState() {
        if (isRecording) {
            voiceButton.setImageResource(R.drawable.ic_mic_recording)
            voiceButton.isSelected = true
        } else {
            voiceButton.setImageResource(R.drawable.ic_mic)
            voiceButton.isSelected = false
        }
    }

    /**
     * 发送语音消息
     */
    private fun sendVoiceMessage(voiceFile: File, duration: Long) {
        val voiceMessage = ChatMessage(
            text = "语音消息 (${duration / 1000}秒)",
            isUser = true,
            timestamp = System.currentTimeMillis(),
            messageType = MessageType.VOICE,
            voiceFilePath = voiceFile.absolutePath,
            voiceDuration = duration
        )

        chatMessages.add(voiceMessage)
        chatAdapter.notifyItemInserted(chatMessages.size - 1)
        scrollToBottom()

        // 模拟AI回复
        simulateAiResponse("语音消息")
    }

    /**
     * 播放语音消息
     */
    private fun playVoiceMessage(message: ChatMessage) {
        if (isPlaying) {
            voicePlayer.stopPlayback()
            return
        }

        val voiceFilePath = message.voiceFilePath
        if (voiceFilePath.isNullOrEmpty()) {
            showToast("语音文件不存在")
            return
        }

        val voiceFile = File(voiceFilePath)
        if (!voiceFile.exists()) {
            showToast("语音文件不存在")
            return
        }

        if (voicePlayer.playVoiceFile(voiceFile)) {
            showToast("开始播放语音")
        } else {
            showToast("播放失败")
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        when (requestCode) {
            REQUEST_RECORD_AUDIO_PERMISSION -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    startRecording()
                } else {
                    showToast("需要录音权限才能使用语音功能")
                }
            }
        }
    }
}