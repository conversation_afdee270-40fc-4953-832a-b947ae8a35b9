package cn.cleartv.launcher.ai.voice

import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import cn.cleartv.launcher.log.Log
import kotlinx.coroutines.*
import java.io.File
import java.io.FileOutputStream
import java.io.IOException

/**
 * 语音录制器
 * 使用 Opus 编码录制语音
 */
class VoiceRecorder {
    
    companion object {
        private const val TAG = "VoiceRecorder"
        private const val SAMPLE_RATE = 16000
        private const val CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_MONO
        private const val AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT
        private const val BUFFER_SIZE_FACTOR = 2
    }
    
    private var audioRecord: AudioRecord? = null
    private var isRecording = false
    private var recordingJob: Job? = null
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    private var onRecordingStateChanged: ((Boolean) -> Unit)? = null
    private var onRecordingComplete: ((File?, Long) -> Unit)? = null
    private var onError: ((String) -> Unit)? = null
    
    /**
     * 设置录制状态变化监听器
     */
    fun setOnRecordingStateChangedListener(listener: (Boolean) -> Unit) {
        onRecordingStateChanged = listener
    }
    
    /**
     * 设置录制完成监听器
     */
    fun setOnRecordingCompleteListener(listener: (File?, Long) -> Unit) {
        onRecordingComplete = listener
    }
    
    /**
     * 设置错误监听器
     */
    fun setOnErrorListener(listener: (String) -> Unit) {
        onError = listener
    }
    
    /**
     * 开始录制
     */
    fun startRecording(outputFile: File): Boolean {
        if (isRecording) {
            Log.w(TAG, "Already recording")
            return false
        }
        
        try {
            val bufferSize = AudioRecord.getMinBufferSize(
                SAMPLE_RATE,
                CHANNEL_CONFIG,
                AUDIO_FORMAT
            ) * BUFFER_SIZE_FACTOR
            
            if (bufferSize == AudioRecord.ERROR || bufferSize == AudioRecord.ERROR_BAD_VALUE) {
                onError?.invoke("无法获取音频缓冲区大小")
                return false
            }
            
            audioRecord = AudioRecord(
                MediaRecorder.AudioSource.MIC,
                SAMPLE_RATE,
                CHANNEL_CONFIG,
                AUDIO_FORMAT,
                bufferSize
            )
            
            if (audioRecord?.state != AudioRecord.STATE_INITIALIZED) {
                onError?.invoke("音频录制器初始化失败")
                return false
            }
            
            audioRecord?.startRecording()
            isRecording = true
            onRecordingStateChanged?.invoke(true)
            
            // 开始录制任务
            recordingJob = scope.launch {
                recordToFile(outputFile, bufferSize)
            }
            
            Log.i(TAG, "Recording started")
            return true
            
        } catch (e: SecurityException) {
            Log.e(TAG, "Permission denied for audio recording", e)
            onError?.invoke("没有录音权限")
            return false
        } catch (e: Exception) {
            Log.e(TAG, "Error starting recording", e)
            onError?.invoke("录制启动失败: ${e.message}")
            return false
        }
    }
    
    /**
     * 停止录制
     */
    fun stopRecording() {
        if (!isRecording) {
            Log.w(TAG, "Not recording")
            return
        }
        
        isRecording = false
        recordingJob?.cancel()
        
        try {
            audioRecord?.stop()
            audioRecord?.release()
            audioRecord = null
            
            onRecordingStateChanged?.invoke(false)
            Log.i(TAG, "Recording stopped")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping recording", e)
            onError?.invoke("停止录制失败: ${e.message}")
        }
    }
    
    /**
     * 录制到文件
     */
    private suspend fun recordToFile(outputFile: File, bufferSize: Int) {
        val startTime = System.currentTimeMillis()
        val tempPcmFile = File(outputFile.parent, "${outputFile.nameWithoutExtension}_temp.pcm")
        
        try {
            // 先录制为 PCM 文件
            FileOutputStream(tempPcmFile).use { outputStream ->
                val buffer = ShortArray(bufferSize / 2)
                val byteBuffer = ByteArray(bufferSize)
                
                while (isRecording && !Thread.currentThread().isInterrupted) {
                    val bytesRead = audioRecord?.read(byteBuffer, 0, bufferSize) ?: 0
                    
                    if (bytesRead > 0) {
                        // Convert bytes to shorts for processing
                        for (i in 0 until bytesRead / 2) {
                            buffer[i] = ((byteBuffer[i * 2 + 1].toInt() shl 8) or 
                                       (byteBuffer[i * 2].toInt() and 0xFF)).toShort()
                        }
                        
                        // Write PCM data
                        outputStream.write(byteBuffer, 0, bytesRead)
                    } else if (bytesRead < 0) {
                        Log.e(TAG, "Error reading audio data: $bytesRead")
                        break
                    }
                }
            }
            
            val duration = System.currentTimeMillis() - startTime
            
            // 将 PCM 转换为 Opus
            withContext(Dispatchers.IO) {
                val success = OpusCodec.encodePcmToOpus(tempPcmFile, outputFile)
                
                // 删除临时 PCM 文件
                if (tempPcmFile.exists()) {
                    tempPcmFile.delete()
                }
                
                withContext(Dispatchers.Main) {
                    if (success && outputFile.exists()) {
                        onRecordingComplete?.invoke(outputFile, duration)
                    } else {
                        onRecordingComplete?.invoke(null, duration)
                        onError?.invoke("语音编码失败")
                    }
                }
            }
            
        } catch (e: IOException) {
            Log.e(TAG, "Error writing audio data", e)
            withContext(Dispatchers.Main) {
                onError?.invoke("录制文件写入失败: ${e.message}")
                onRecordingComplete?.invoke(null, 0)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Log.e(TAG, "Unexpected error during recording", e)
            withContext(Dispatchers.Main) {
                onError?.invoke("录制过程中发生错误: ${e.message}")
                onRecordingComplete?.invoke(null, 0)
            }
        } finally {
            // 清理临时文件
            if (tempPcmFile.exists()) {
                tempPcmFile.delete()
            }
        }
    }
    
    /**
     * 是否正在录制
     */
    fun isRecording(): Boolean = isRecording
    
    /**
     * 释放资源
     */
    fun release() {
        stopRecording()
        scope.cancel()
    }
}
