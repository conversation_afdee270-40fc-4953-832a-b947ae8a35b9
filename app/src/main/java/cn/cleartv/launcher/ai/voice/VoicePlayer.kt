package cn.cleartv.launcher.ai.voice

import android.media.AudioAttributes
import android.media.AudioFormat
import android.media.AudioManager
import android.media.AudioTrack
import cn.cleartv.launcher.log.Log
import kotlinx.coroutines.*
import java.io.File

/**
 * 语音播放器
 * 用于播放 Opus 编码的语音文件
 */
class VoicePlayer {
    
    companion object {
        private const val TAG = "VoicePlayer"
        private const val SAMPLE_RATE = 16000
        private const val CHANNEL_CONFIG = AudioFormat.CHANNEL_OUT_MONO
        private const val AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT
    }
    
    private var audioTrack: AudioTrack? = null
    private var isPlaying = false
    private var playingJob: Job? = null
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    private var onPlaybackStateChanged: ((Boolean) -> Unit)? = null
    private var onPlaybackComplete: (() -> Unit)? = null
    private var onError: ((String) -> Unit)? = null
    
    /**
     * 设置播放状态变化监听器
     */
    fun setOnPlaybackStateChangedListener(listener: (Boolean) -> Unit) {
        onPlaybackStateChanged = listener
    }
    
    /**
     * 设置播放完成监听器
     */
    fun setOnPlaybackCompleteListener(listener: () -> Unit) {
        onPlaybackComplete = listener
    }
    
    /**
     * 设置错误监听器
     */
    fun setOnErrorListener(listener: (String) -> Unit) {
        onError = listener
    }
    
    /**
     * 播放语音文件
     */
    fun playVoiceFile(voiceFile: File): Boolean {
        if (isPlaying) {
            Log.w(TAG, "Already playing")
            return false
        }
        
        if (!voiceFile.exists()) {
            onError?.invoke("语音文件不存在")
            return false
        }
        
        try {
            val bufferSize = AudioTrack.getMinBufferSize(
                SAMPLE_RATE,
                CHANNEL_CONFIG,
                AUDIO_FORMAT
            )
            
            if (bufferSize == AudioTrack.ERROR || bufferSize == AudioTrack.ERROR_BAD_VALUE) {
                onError?.invoke("无法获取音频缓冲区大小")
                return false
            }
            
            val audioAttributes = AudioAttributes.Builder()
                .setUsage(AudioAttributes.USAGE_MEDIA)
                .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
                .build()
            
            val audioFormat = AudioFormat.Builder()
                .setSampleRate(SAMPLE_RATE)
                .setChannelMask(CHANNEL_CONFIG)
                .setEncoding(AUDIO_FORMAT)
                .build()
            
            audioTrack = AudioTrack(
                audioAttributes,
                audioFormat,
                bufferSize,
                AudioTrack.MODE_STREAM,
                AudioManager.AUDIO_SESSION_ID_GENERATE
            )
            
            if (audioTrack?.state != AudioTrack.STATE_INITIALIZED) {
                onError?.invoke("音频播放器初始化失败")
                return false
            }
            
            audioTrack?.play()
            isPlaying = true
            onPlaybackStateChanged?.invoke(true)
            
            // 开始播放任务
            playingJob = scope.launch {
                playFile(voiceFile)
            }
            
            Log.i(TAG, "Playback started")
            return true
            
        } catch (e: Exception) {
            Log.e(TAG, "Error starting playback", e)
            onError?.invoke("播放启动失败: ${e.message}")
            return false
        }
    }
    
    /**
     * 停止播放
     */
    fun stopPlayback() {
        if (!isPlaying) {
            Log.w(TAG, "Not playing")
            return
        }
        
        isPlaying = false
        playingJob?.cancel()
        
        try {
            audioTrack?.stop()
            audioTrack?.release()
            audioTrack = null
            
            onPlaybackStateChanged?.invoke(false)
            Log.i(TAG, "Playback stopped")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping playback", e)
            onError?.invoke("停止播放失败: ${e.message}")
        }
    }
    
    /**
     * 播放文件
     */
    private suspend fun playFile(voiceFile: File) {
        val tempPcmFile = File(voiceFile.parent, "${voiceFile.nameWithoutExtension}_play_temp.pcm")
        
        try {
            // 先将 Opus 解码为 PCM
            val decodeSuccess = OpusCodec.decodeOpusToPcm(voiceFile, tempPcmFile)
            if (!decodeSuccess || !tempPcmFile.exists()) {
                withContext(Dispatchers.Main) {
                    onError?.invoke("语音解码失败")
                    onPlaybackComplete?.invoke()
                }
                return
            }
            
            // 播放 PCM 数据
            tempPcmFile.inputStream().use { inputStream ->
                val buffer = ByteArray(4096)
                
                while (isPlaying && !Thread.currentThread().isInterrupted) {
                    val bytesRead = inputStream.read(buffer)
                    if (bytesRead <= 0) break
                    
                    val bytesWritten = audioTrack?.write(buffer, 0, bytesRead) ?: 0
                    if (bytesWritten < 0) {
                        Log.e(TAG, "Error writing audio data: $bytesWritten")
                        break
                    }
                }
            }
            
            // 等待播放完成
            if (isPlaying) {
                audioTrack?.let { track ->
                    // 等待缓冲区播放完毕
                    while (track.playState == AudioTrack.PLAYSTATE_PLAYING && 
                           track.playbackHeadPosition < track.bufferSizeInFrames) {
                        delay(10)
                    }
                }
            }
            
            withContext(Dispatchers.Main) {
                onPlaybackComplete?.invoke()
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error during playback", e)
            withContext(Dispatchers.Main) {
                onError?.invoke("播放过程中发生错误: ${e.message}")
                onPlaybackComplete?.invoke()
            }
        } finally {
            // 清理临时文件
            if (tempPcmFile.exists()) {
                tempPcmFile.delete()
            }
            
            // 停止播放
            withContext(Dispatchers.Main) {
                if (isPlaying) {
                    stopPlayback()
                }
            }
        }
    }
    
    /**
     * 是否正在播放
     */
    fun isPlaying(): Boolean = isPlaying
    
    /**
     * 释放资源
     */
    fun release() {
        stopPlayback()
        scope.cancel()
    }
}
