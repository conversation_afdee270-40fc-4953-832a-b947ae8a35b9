package cn.cleartv.launcher.log

import android.os.Looper
import android.os.Process
import cn.cleartv.launcher.Dirs
import cn.cleartv.launcher.GlobalInfo
import cn.cleartv.launcher.toJsonString
import com.tencent.mars.xlog.Xlog
import java.io.File


object Log {

  init {
    System.loadLibrary("c++_shared");
    System.loadLibrary("marsxlog");
  }

  const val DEFAULT_TAG: String = "ClearLauncher"
  const val NAME_PREFIX: String = "ClearLauncher"
  val logPath: File = Dirs.logDir
  val logCachePath: File = Dirs.logCacheDir

  private val xlog: Xlog by lazy {
    Xlog()
  }

  fun init() {
    val logConfig: Xlog.XLogConfig = Xlog.XLogConfig()
    logConfig.mode = Xlog.AppednerModeAsync
    logConfig.logdir = logPath.absolutePath
    logConfig.nameprefix = NAME_PREFIX
    logConfig.pubkey = ""
    logConfig.compressmode = Xlog.ZLIB_MODE
    logConfig.compresslevel = 0
    logConfig.cachedir = logCachePath.absolutePath
    logConfig.cachedays = 0
    if (GlobalInfo.isDebug) {
      logConfig.level = Xlog.LEVEL_VERBOSE
      xlog.setConsoleLogOpen(0L, true)
    } else {
      logConfig.level = Xlog.LEVEL_INFO
      xlog.setConsoleLogOpen(0L, false)
    }
    xlog.appenderOpen(
      logConfig.level,
      logConfig.mode,
      logConfig.cachedir,
      logConfig.logdir,
      logConfig.nameprefix,
      logConfig.cachedays
    )
  }

  fun flush() {
    xlog.appenderFlush(0L, true)
  }

  private fun Any.toFormatString(): String {
    if (this is String) return this
    if (this is Throwable) return this.stackTraceToString()
    return this.toJsonString()
  }

  private fun Array<Any>.toFormatString(): String {
    return this.joinToString("; ") { it.toFormatString() }
  }

  fun f(msg: Any?) {
    f(null, msg?.toFormatString())
  }

  fun e(msg: Any?) {
    e(null, msg?.toFormatString())
  }

  fun w(msg: Any?) {
    w(null, msg?.toFormatString())
  }

  fun i(msg: Any?) {
    i(null, msg?.toFormatString())
  }

  fun d(msg: Any?) {
    d(null, msg?.toFormatString())
  }

  fun v(msg: Any?) {
    v(null, msg?.toFormatString())
  }

  fun v(tag: String?, vararg msg: Any?) {
    if (xlog.getLogLevel(0L) <= Xlog.LEVEL_VERBOSE) {
      val element: StackTraceElement? = Throwable().stackTrace.getOrNull(2)
      xlog.logW(
        0L,
        tag ?: element?.fileName?.substringBefore('.') ?: DEFAULT_TAG,
        element?.fileName ?: "",
        element?.methodName ?: "",
        element?.lineNumber ?: 0,
        Process.myPid(),
        Thread.currentThread().id,
        Looper.getMainLooper().thread.id,
        msg.toFormatString()
      )
    }
  }

  fun d(tag: String?, vararg msg: Any?) {
    if (xlog.getLogLevel(0L) <= Xlog.LEVEL_DEBUG) {
      val element: StackTraceElement? = Throwable().stackTrace.getOrNull(2)
      xlog.logD(
        0L,
        tag ?: element?.fileName?.substringBefore('.') ?: DEFAULT_TAG,
        element?.fileName ?: "",
        element?.methodName ?: "",
        element?.lineNumber ?: 0,
        Process.myPid(),
        Thread.currentThread().id,
        Looper.getMainLooper().thread.id,
        msg.toFormatString()
      )
    }
  }

  fun i(tag: String?, vararg msg: Any?) {
    if (xlog.getLogLevel(0L) <= Xlog.LEVEL_INFO) {
      val element: StackTraceElement? = Throwable().stackTrace.getOrNull(2)
      xlog.logI(
        0L,
        tag ?: element?.fileName?.substringBefore('.') ?: DEFAULT_TAG,
        element?.fileName ?: "",
        element?.methodName ?: "",
        element?.lineNumber ?: 0,
        Process.myPid(),
        Thread.currentThread().id,
        Looper.getMainLooper().thread.id,
        msg.toFormatString()
      )
    }
  }

  fun w(tag: String?, vararg msg: Any?) {
    if (xlog.getLogLevel(0L) <= Xlog.LEVEL_WARNING) {
      val element: StackTraceElement? = Throwable().stackTrace.getOrNull(2)
      xlog.logW(
        0L,
        tag ?: element?.fileName?.substringBefore('.') ?: DEFAULT_TAG,
        element?.fileName ?: "",
        element?.methodName ?: "",
        element?.lineNumber ?: 0,
        Process.myPid(),
        Thread.currentThread().id,
        Looper.getMainLooper().thread.id,
        msg.toFormatString()
      )
    }
  }

  fun e(tag: String?, vararg msg: Any?) {
    if (xlog.getLogLevel(0L) <= Xlog.LEVEL_ERROR) {
      val element: StackTraceElement? = Throwable().stackTrace.getOrNull(2)
      xlog.logE(
        0L,
        tag ?: element?.fileName?.substringBefore('.') ?: DEFAULT_TAG,
        element?.fileName ?: "",
        element?.methodName ?: "",
        element?.lineNumber ?: 0,
        Process.myPid(),
        Thread.currentThread().id,
        Looper.getMainLooper().thread.id,
        msg.toFormatString()
      )
    }
  }

  fun f(tag: String?, vararg msg: Any?) {
    if (xlog.getLogLevel(0L) <= Xlog.LEVEL_FATAL) {
      val element: StackTraceElement? = Throwable().stackTrace.getOrNull(2)
      xlog.logI(
        0L,
        tag ?: element?.fileName?.substringBefore('.') ?: DEFAULT_TAG,
        element?.fileName ?: "",
        element?.methodName ?: "",
        element?.lineNumber ?: 0,
        Process.myPid(),
        Thread.currentThread().id,
        Looper.getMainLooper().thread.id,
        msg.toFormatString()
      )
    }
  }

}