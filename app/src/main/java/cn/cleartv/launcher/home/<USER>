package cn.cleartv.launcher.home

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.view.View
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.children
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import cn.cleartv.launcher.ClearService
import cn.cleartv.launcher.R
import cn.cleartv.launcher.ai.AiAssistantActivity
import cn.cleartv.launcher.home.adapter.AppGridAdapter
import cn.cleartv.launcher.home.adapter.AppListAdapter
import cn.cleartv.launcher.enableFullScreen
import cn.cleartv.launcher.home.model.AppInfo
import cn.cleartv.launcher.log.Log
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.floatingactionbutton.FloatingActionButton
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class HomeActivity : AppCompatActivity() {
  private lateinit var dateTimeText: TextView
  private lateinit var bottomSheetBehavior: BottomSheetBehavior<View>
  private lateinit var appGridView: RecyclerView
  private lateinit var appListView: RecyclerView
  private lateinit var appGridAdapter: AppGridAdapter
  private lateinit var appListAdapter: AppListAdapter
  private lateinit var aiAssistantFab: FloatingActionButton
  private val dateTimeFormat = SimpleDateFormat("yyyy年MM月dd日\nEEEE\nHH:mm", Locale.getDefault())
  private lateinit var timeReceiver: BroadcastReceiver
  private val PREFS_NAME = "LauncherPrefs"
  private val KEY_SELECTED_APPS = "selected_apps"

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    Log.i("onCreate")
    enableFullScreen()
    // 隐藏状态栏并设置全屏模式
    window.decorView.systemUiVisibility = (
        View.SYSTEM_UI_FLAG_FULLSCREEN or
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        )
    setContentView(R.layout.activity_home)

    // 初始化视图
    dateTimeText = findViewById(R.id.dateTimeText)
    aiAssistantFab = findViewById(R.id.aiAssistantFab)
    appGridView = findViewById(R.id.appGridView)
    appGridView.nextFocusDownId = R.id.appListView
    appListView = findViewById(R.id.appListView)
    appListView.nextFocusUpId = R.id.appGridView
    appGridView.setOnFocusChangeListener { v, hasFocus ->
      if (hasFocus) {
        if (bottomSheetBehavior.state == BottomSheetBehavior.STATE_EXPANDED) {
          bottomSheetBehavior.state = BottomSheetBehavior.STATE_COLLAPSED
        }
      }
    }
    appListView.setOnFocusChangeListener { v, hasFocus ->
      if (hasFocus) {
        if (bottomSheetBehavior.state == BottomSheetBehavior.STATE_COLLAPSED) {
          bottomSheetBehavior.state = BottomSheetBehavior.STATE_EXPANDED
          appListView.children.firstOrNull()?.requestFocus()
        }
      }
    }
    val bottomSheet = findViewById<ConstraintLayout>(R.id.bottomSheet)

    // 设置底部抽屉行为
    bottomSheetBehavior = BottomSheetBehavior.from(bottomSheet)
    bottomSheetBehavior.state = BottomSheetBehavior.STATE_COLLAPSED

    // 设置AI助手FAB点击事件
    aiAssistantFab.setOnClickListener {
      val intent = Intent(this, AiAssistantActivity::class.java)
      startActivity(intent)
    }

    // 初始化应用列表
    setupAppGridView()
    setupAppListView()
    loadApps()

    // 更新时间的定时器
    startTimeUpdates()

    val serviceIntent = Intent(this, ClearService::class.java)
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
      startForegroundService(serviceIntent)
    } else {
      startService(serviceIntent)
    }
  }

  private fun setupAppGridView() {
    appGridAdapter = AppGridAdapter().apply {
      setOnItemClickListener { app ->
        launchApp(app.packageName)
      }
      setOnItemLongClickListener {
        showAppSelectionDialog()
        true
      }
    }
    appGridView.apply {
      layoutManager = GridLayoutManager(this@HomeActivity, 2)
      adapter = appGridAdapter
      layoutParams = (layoutParams as ConstraintLayout.LayoutParams).apply {
        width = ConstraintLayout.LayoutParams.WRAP_CONTENT
        height = ConstraintLayout.LayoutParams.WRAP_CONTENT
        topToBottom = R.id.dateTimeText
        bottomToTop = R.id.bottomSheet
        startToStart = ConstraintLayout.LayoutParams.PARENT_ID
        endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
        verticalBias = 0.5f
      }
    }
  }

  private fun showAppSelectionDialog() {
    val allApps = getAllApps()
    val selectedApps = getSelectedApps().toMutableSet()

    val appNames = allApps.map { it.appName }.toTypedArray()
    val checkedItems = allApps.map { selectedApps.contains(it.packageName) }.toBooleanArray()

    AlertDialog.Builder(this)
      .setTitle("选择要显示的应用")
      .setMultiChoiceItems(
        appNames,
        checkedItems
      ) { dialog: DialogInterface, which: Int, isChecked: Boolean ->
        val packageName = allApps[which].packageName
        if (isChecked) {
          if (selectedApps.size < 4) {
            selectedApps.add(packageName)
          } else {
            false
          }
        } else {
          selectedApps.remove(packageName)
        }
      }
      .setPositiveButton("确定") { dialog: DialogInterface, which: Int ->
        saveSelectedApps(selectedApps)
        updateGridApps()
      }
      .setNegativeButton("取消", null)
      .show()
  }

  private fun getAllApps(): List<AppInfo> {
    val packageManager = packageManager
    val intent = Intent(Intent.ACTION_MAIN, null).apply {
      addCategory(Intent.CATEGORY_LAUNCHER)
    }
    return packageManager.queryIntentActivities(intent, PackageManager.MATCH_ALL)
      .map { resolveInfo ->
        AppInfo(
          packageName = resolveInfo.activityInfo.packageName,
          appName = resolveInfo.loadLabel(packageManager).toString(),
          icon = resolveInfo.loadIcon(packageManager),
          isSystemApp = (resolveInfo.activityInfo.applicationInfo.flags and ApplicationInfo.FLAG_SYSTEM) != 0
        )
      }
      .sortedBy { it.appName }
  }

  private fun getSelectedApps(): Set<String> {
    val prefs = getSharedPreferences(PREFS_NAME, MODE_PRIVATE)
    return prefs.getStringSet(KEY_SELECTED_APPS, setOf()) ?: setOf()
  }

  private fun saveSelectedApps(selectedApps: Set<String>) {
    val prefs = getSharedPreferences(PREFS_NAME, MODE_PRIVATE)
    prefs.edit().putStringSet(KEY_SELECTED_APPS, selectedApps).apply()
  }

  private fun updateGridApps() {
    val selectedPackages = getSelectedApps()
    val selectedApps = getAllApps().filter { it.packageName in selectedPackages }
    appGridAdapter.submitList(selectedApps)
  }

  private fun loadApps() {
    val allApps = getAllApps()
    appListAdapter.submitList(allApps)

    // 加载已选择的应用到网格视图
    val selectedPackages = getSelectedApps()
    if (selectedPackages.isEmpty()) {
      // 如果没有选择过应用，默认显示前4个非系统应用
      appGridAdapter.submitList(allApps.filterNot { it.isSystemApp }.take(4))
    } else {
      val selectedApps = allApps.filter { it.packageName in selectedPackages }
      appGridAdapter.submitList(selectedApps)
    }
  }

  private fun setupAppListView() {
    appListAdapter = AppListAdapter()
    appListView.apply {
      layoutManager = LinearLayoutManager(this@HomeActivity)
      adapter = appListAdapter
    }
  }


  private fun launchApp(packageName: String) {
    val intent = packageManager.getLaunchIntentForPackage(packageName)
    intent?.let { startActivity(it) }
  }

  private fun startTimeUpdates() {
    // 立即更新一次时间
    updateDateTime()

    // 注册时间变化的广播接收器
    timeReceiver = object : BroadcastReceiver() {
      override fun onReceive(context: Context?, intent: Intent?) {
        if (intent?.action == Intent.ACTION_TIME_TICK) {
          updateDateTime()
        }
      }
    }
    registerReceiver(timeReceiver, IntentFilter(Intent.ACTION_TIME_TICK))
  }

  private fun updateDateTime() {
    dateTimeText.text = dateTimeFormat.format(Date())
  }

  @SuppressLint("MissingSuperCall")
  override fun onBackPressed() {
    if (bottomSheetBehavior.state == BottomSheetBehavior.STATE_EXPANDED) {
      // 如果底部应用列表是展开状态，则收起列表
      bottomSheetBehavior.state = BottomSheetBehavior.STATE_COLLAPSED
    } else {
      // 如果底部应用列表已经是收起状态，则拦截返回事件（不调用super.onBackPressed()）
      // 什么都不做，保持在桌面
    }
  }

  override fun onDestroy() {
    super.onDestroy()
    unregisterReceiver(timeReceiver)
  }

}