package cn.cleartv.launcher

import cn.cleartv.launcher.services.annotation.LauncherType
import cn.cleartv.launcher.services.model.LauncherData
import cn.cleartv.launcher.settings.DataStoreUtils

object Configs {

  var hostUrl: String
    set(value) = DataStoreUtils.putSyncData("hostUrl", value)
    get() = DataStoreUtils.getSyncData("hostUrl", "http://**************:8081")

  var launcherData: LauncherData
    set(value) {
      DataStoreUtils.putSyncData("launcherType", value.type)
      DataStoreUtils.putSyncData("launcherPath", value.path)
      DataStoreUtils.putSyncData("launcherParams", value.params)
    }
    get() = LauncherData(
      type = DataStoreUtils.getSyncData("launcherType", LauncherType.DEFAULT),
      path = DataStoreUtils.getSyncData("launcherPath", ""),
      params = DataStoreUtils.getSyncData("launcherParams", ""),
    )

  var name: String
    set(value) = DataStoreUtils.putSyncData("name", value)
    get() = DataStoreUtils.getSyncData("name", "清鹤终端")


  var description: String
    set(value) = DataStoreUtils.putSyncData("description", value)
    get() = DataStoreUtils.getSyncData("description", "")

  var remark: String
    set(value) = DataStoreUtils.putSyncData("remark", value)
    get() = DataStoreUtils.getSyncData("remark", "")

  var type: String
    set(value) = DataStoreUtils.putSyncData("type", value)
    get() = DataStoreUtils.getSyncData("type", "default")

  var location: String
    set(value) = DataStoreUtils.putSyncData("location", value)
    get() = DataStoreUtils.getSyncData("location", "")

  var extraInfo: String
    set(value) = DataStoreUtils.putSyncData("extraInfo", value)
    get() = DataStoreUtils.getSyncData("extraInfo", "{}")

  data class Config(
    val hostUrl: String?,
    val launcherData: LauncherData?,
  )

  var config: Config
    set(value) {
      value.hostUrl?.let { hostUrl = it }
      value.launcherData?.let { launcherData = it }
    }
    get() = Config(hostUrl, launcherData)
}
