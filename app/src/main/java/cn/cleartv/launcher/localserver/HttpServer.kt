package cn.cleartv.launcher.localserver

import cn.cleartv.launcher.App
import cn.cleartv.launcher.Dirs
import cn.cleartv.launcher.exception.AuthFailedError
import cn.cleartv.launcher.exception.BadRequestException
import cn.cleartv.launcher.exception.MethodNotAllowedException
import cn.cleartv.launcher.exception.ServiceMethodError
import cn.cleartv.launcher.localserver.nanohttpd.NanoHTTPD
import cn.cleartv.launcher.localserver.nanohttpd.webserver.InternalRewrite
import cn.cleartv.launcher.localserver.nanohttpd.webserver.SimpleWebServer
import cn.cleartv.launcher.localserver.nanohttpd.webserver.WebServerPlugin
import cn.cleartv.launcher.log.Log
import cn.cleartv.launcher.services.AuthService
import cn.cleartv.launcher.services.BrowserService
import cn.cleartv.launcher.services.CommonService
import cn.cleartv.launcher.services.DataStoreService
import cn.cleartv.launcher.services.LauncherService
import cn.cleartv.launcher.services.LogService
import cn.cleartv.launcher.services.NetworkService
import cn.cleartv.launcher.services.PluginService
import cn.cleartv.launcher.services.RtcService
import cn.cleartv.launcher.services.ShellService
import cn.cleartv.launcher.services.TtsService
import cn.cleartv.launcher.services.UploadService
import cn.cleartv.launcher.services.model.ResponseData
import cn.cleartv.launcher.toJsonString
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import java.io.File
import java.io.FileInputStream
import java.io.FileNotFoundException
import java.io.IOException
import java.io.UnsupportedEncodingException
import java.net.URLEncoder
import java.security.KeyStore
import java.util.Collections
import java.util.StringTokenizer
import javax.net.ssl.KeyManagerFactory
import javax.net.ssl.SSLContext
import javax.net.ssl.TrustManagerFactory


class HttpServer(port: Int) : NanoHTTPD(port) {

  val useSSL = false // 是否开启HTTPS
  val needClientAuth = false // 是否开启双向认证，请求端需要导入认证证书

  private val scope = CoroutineScope(Dispatchers.IO + Job())

  init {
    if (useSSL) {
      try {
        val type = KeyStore.getDefaultType()
        Log.i("KeyStore Type: $type")
        val keyStore = KeyStore.getInstance(type).apply {
          val keystoreFile = if ("BKS".equals(type, true)) {
            "keystore.bks"
          } else {
            "keystore.jks"
          }
          load(App.instance.assets.open(keystoreFile), "clear!@#".toCharArray())
        }
        val keyManagerFactory =
          KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm())
        keyManagerFactory.init(keyStore, "clear!@#".toCharArray())

        val trustManagerFactory =
          TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm())
        val trueKeyStore = if (needClientAuth) {
          val keystoreFile = if ("BKS".equals(type, true)) {
            "trust_keystore.bks"
          } else {
            "trust_keystore.jks"
          }
          KeyStore.getInstance(type).apply {
            keyStore.load(App.instance.assets.open(keystoreFile), "clear!@#".toCharArray())
          }
        } else {
          keyStore
        }
        trustManagerFactory.init(trueKeyStore)

        val ctx = SSLContext.getInstance("TLS")
        ctx.init(keyManagerFactory.keyManagers, trustManagerFactory.trustManagers, null)

        makeSecure(ctx.serverSocketFactory, null, needClientAuth)
      } catch (e: Exception) {
        e.printStackTrace()
      }
    }

    // 注册服务
    ServiceManager.registerService(AuthService)
    ServiceManager.registerService(BrowserService)
    ServiceManager.registerService(CommonService)
    ServiceManager.registerService(DataStoreService)
    ServiceManager.registerService(LauncherService)
    ServiceManager.registerService(LogService)
    ServiceManager.registerService(NetworkService)
    ServiceManager.registerService(ShellService)
    ServiceManager.registerService(TtsService)
    ServiceManager.registerService(UploadService)
    ServiceManager.registerService(RtcService)
    ServiceManager.registerService(PluginService)

  }

  fun normalizeUri(value: String?): String {
    if (value == null) {
      return ""
    }
    return value.trim().replace("/{2,}".toRegex(), "/")
  }

  fun newRedirectResponse(redirectUrl: String): Response {
    val res = SimpleWebServer.newFixedLengthResponse(
      Response.Status.REDIRECT, MIME_HTML,
      "<html><body>Redirected: <a href=\"$redirectUrl\">$redirectUrl</a></body></html>"
    )
    res.addHeader("Location", redirectUrl)
    return res
  }

  override fun serve(session: IHTTPSession): Response {
    try {
      val realUri = normalizeUri(session.uri)
      val method = session.method
      if (method == Method.GET) {

        if ("/favicon.ico" == realUri) {
          return newChunkedResponse(
            Response.Status.OK,
            "image/*",
            App.instance.assets.open("web/logo.webp")
          )
        } else if (realUri.startsWith("/web/") || realUri.startsWith("/docs/")) {
          return newChunkedResponse(
            Response.Status.OK,
            getMimeTypeForFile(realUri),
            App.instance.assets.open(realUri.substring(1))
          )
        } else if (realUri.startsWith("/upload")) {
          return fileRespond(
            Collections.unmodifiableMap(session.headers),
            session,
            realUri,
            "/upload",
            listOf(Dirs.uploadDir)
          )
        } else if (realUri.startsWith("/log")) {
          Log.flush()
          return fileRespond(
            Collections.unmodifiableMap(session.headers),
            session,
            realUri,
            "/log",
            listOf(Dirs.logDir)
          )
        } else if (realUri.startsWith("/frontend")) {
          if (realUri == "/frontend") {
            val redirectUrl = "/frontend/index.html"
            return newRedirectResponse(redirectUrl)
          }
          return newChunkedResponse(
            Response.Status.OK,
            getMimeTypeForFile(realUri),
            FileInputStream(File(Dirs.wwwDir, realUri))
          )
        }
      }

      // api service manager
      return ServiceManager.invokeRequest(session).apply {
        // 支持跨域
        addHeader("Access-Control-Allow-Origin", "*")
        addHeader("Access-Control-Allow-Credentials", "true")
        addHeader(
          "Access-Control-Allow-Methods",
          "GET,POST,PUT,DELETE,OPTIONS,HEAD"
        )
      }
    } catch (methodNotAllowedException: MethodNotAllowedException) {
      return getMethodNotAllowedResponse(methodNotAllowedException.message)
    } catch (badRequestException: BadRequestException) {
      return getBadRequestResponse(badRequestException.message)
    } catch (serviceMethodError: ServiceMethodError) {
      return getInternalErrorResponse(serviceMethodError)
    } catch (e: FileNotFoundException) {
      return getNotFoundResponse(e.message)
    } catch (e: AuthFailedError) {
      return getForbiddenResponse(e.message ?: "Auth failed")
    } catch (e: Exception) {
      return getInternalErrorResponse(e)
    }
  }

  private fun fileRespond(
    headers: Map<String, String>,
    session: IHTTPSession,
    uri: String,
    prefix: String,
    rootDirs: List<File>
  ): Response {
    // Remove URL arguments
    var finalUri = uri.removePrefix(prefix).trim { it <= ' ' }.replace(File.separatorChar, '/')
    if (finalUri.indexOf('?') >= 0) {
      finalUri = finalUri.substring(0, uri.indexOf('?'))
    }

    // Prohibit getting out of current directory
    if (finalUri.contains("../")) {
      return getForbiddenResponse("Won't serve ../ for security reasons.")
    }

    var canServeUri = false
    var homeDir: File? = null
    var i = 0
    while (!canServeUri && i < rootDirs.size) {
      homeDir = rootDirs[i]
      canServeUri = canServeUri(finalUri, homeDir)
      i++
    }
    if (!canServeUri) {
      return getForbiddenResponse("can't read file")
    }

    // Browsers get confused without '/' after the directory, send a
    // redirect.
    val f = File(homeDir, finalUri)
    if (f.isDirectory && !finalUri.endsWith("/")) {
      val redirectUrl = "$uri/"
      return newRedirectResponse(redirectUrl)
    }

    if (f.isDirectory) {
      // First look for index files (index.html, index.htm, etc) and if
      // none found, list the directory if readable.
      val indexFile: String? = findIndexFileInDirectory(f)
      return if (indexFile == null) {
        if (f.canRead()) {
          // No index file, list the directory if it is readable
          SimpleWebServer.newFixedLengthResponse(
            Response.Status.OK,
            MIME_HTML,
            listDirectory(finalUri, f)
          )
        } else {
          getForbiddenResponse("No directory listing.")
        }
      } else {
        fileRespond(headers, session, uri + indexFile, prefix, rootDirs)
      }
    }
    val mimeTypeForFile = getMimeTypeForFile(uri)
    val plugin = mimeTypeHandlers[mimeTypeForFile]
    var response: Response? = null
    if (plugin != null && plugin.canServeUri(uri, homeDir)) {
      response = plugin.serveFile(uri, headers, session, f, mimeTypeForFile)
      if (response != null && response is InternalRewrite) {
        val rewrite = response
        return fileRespond(rewrite.headers, session, rewrite.uri, prefix, rootDirs)
      }
    } else {
      response = serveFile(finalUri, headers, f, mimeTypeForFile)
    }
    return response ?: getNotFoundResponse("File not found")
  }

  val mimeTypeHandlers: Map<String, WebServerPlugin> = java.util.HashMap()
  private fun canServeUri(uri: String, homeDir: File): Boolean {
    var canServeUri: Boolean
    val f = File(homeDir, uri)
    canServeUri = f.exists()
    if (!canServeUri) {
      val plugin = mimeTypeHandlers[getMimeTypeForFile(uri)]
      if (plugin != null) {
        canServeUri = plugin.canServeUri(uri, homeDir)
      }
    } else {
      // fixed 不能读取的文件夹也过滤
      canServeUri = f.canRead()
    }
    return canServeUri
  }

  private fun findIndexFileInDirectory(directory: File): String? {
    for (fileName in SimpleWebServer.INDEX_FILE_NAMES) {
      val indexFile = File(directory, fileName)
      if (indexFile.isFile) {
        return fileName
      }
    }
    return null
  }

  protected fun listDirectory(uri: String, f: File): String {
    val heading = "Directory $uri"
    //        String heading = f.getAbsolutePath() + uri;
    val msg =
      StringBuilder(
        ("""
  <html><head><title>$heading</title><style><!--
  span.dirname { font-weight: bold; }
  span.filesize { font-size: 75%; }
  // -->
  </style></head><body><h1>$heading</h1>
  """.trimIndent())
      )

    var up: String? = null
    if (uri.length > 1) {
      val u = uri.substring(0, uri.length - 1)
      val slash = u.lastIndexOf('/')
      if (slash >= 0 && slash < u.length) {
        up = uri.substring(0, slash + 1)
      }
    }

    val files = f.list { dir, name ->
      File(
        dir,
        name
      ).isFile
    }?.let { mutableListOf(*it) } ?: mutableListOf()
    files.sort()
    val directories = f.list { dir, name ->
      File(
        dir,
        name
      ).isDirectory
    }?.let { mutableListOf(*it) } ?: mutableListOf()
    directories.sort()
    if (up != null || directories.size + files.size > 0) {
      msg.append("<ul>")
      if (up != null || directories.size > 0) {
        msg.append("<section class=\"directories\">")
        if (up != null) {
          if (up == "/") up = "../"
          msg.append("<li><a rel=\"directory\" href=\"./").append(up)
            .append("\"><span class=\"dirname\">..</span></a></li>")
        }
        for (directory in directories) {
          val dir = "$directory/"
          msg.append("<li><a rel=\"directory\" href=\"./")
            .append(encodeUri(uri + dir).removePrefix("/"))
            .append("\"><span class=\"dirname\">").append(dir).append("</span></a></li>")
        }
        msg.append("</section>")
      }
      if (files.size > 0) {
        msg.append("<section class=\"files\">")
        for (file in files) {
          msg.append("<li><a href=\"./").append(encodeUri(file))
            .append("\"><span class=\"filename\">").append(file).append("</span></a>")
          val curFile = File(f, file)
          val len = curFile.length()
          msg.append("&nbsp;<span class=\"filesize\">(")
          if (len < 1024) {
            msg.append(len).append(" bytes")
          } else if (len < 1024 * 1024) {
            msg.append(len / 1024).append(".").append(len % 1024 / 10 % 100).append(" KB")
          } else {
            msg.append(len / (1024 * 1024)).append(".").append(len % (1024 * 1024) / 10000 % 100)
              .append(" MB")
          }
          msg.append(")</span></li>")
        }
        msg.append("</section>")
      }
      msg.append("</ul>")
    }
    msg.append("</body></html>")
    return msg.toString()
  }

  private fun encodeUri(uri: String): String {
    var newUri = ""
    val st = StringTokenizer(uri, "/ ", true)
    while (st.hasMoreTokens()) {
      val tok = st.nextToken()
      if ("/" == tok) {
        newUri += "/"
      } else if (" " == tok) {
        newUri += "%20"
      } else {
        try {
          newUri += URLEncoder.encode(tok, "UTF-8")
        } catch (ignored: UnsupportedEncodingException) {
        }
      }
    }
    return newUri
  }

  /**
   * Serves file from homeDir and its' subdirectories (only). Uses only URI,
   * ignores all headers and HTTP parameters.
   */
  fun serveFile(uri: String?, header: Map<String, String>, file: File, mime: String): Response {
    var res: Response
    try {
      // Calculate etag
      val etag =
        Integer.toHexString((file.absolutePath + file.lastModified() + "" + file.length()).hashCode())

      // Support (simple) skipping:
      var startFrom: Long = 0
      var endAt: Long = -1
      var range = header["range"]
      if (range != null) {
        if (range.startsWith("bytes=")) {
          range = range.substring("bytes=".length)
          val minus = range.indexOf('-')
          try {
            if (minus > 0) {
              startFrom = range.substring(0, minus).toLong()
              endAt = range.substring(minus + 1).toLong()
            }
          } catch (ignored: NumberFormatException) {
          }
        }
      }

      // get if-range header. If present, it must match etag or else we
      // should ignore the range request
      val ifRange = header["if-range"]
      val headerIfRangeMissingOrMatching = (ifRange == null || etag == ifRange)

      val ifNoneMatch = header["if-none-match"]
      val headerIfNoneMatchPresentAndMatching =
        ifNoneMatch != null && ("*" == ifNoneMatch || ifNoneMatch == etag)

      // Change return code and add Content-Range header when skipping is
      // requested
      val fileLen = file.length()

      if (headerIfRangeMissingOrMatching && range != null && startFrom >= 0 && startFrom < fileLen) {
        // range request that matches current etag
        // and the startFrom of the range is satisfiable
        if (headerIfNoneMatchPresentAndMatching) {
          // range request that matches current etag
          // and the startFrom of the range is satisfiable
          // would return range from file
          // respond with not-modified
          res = SimpleWebServer.newFixedLengthResponse(Response.Status.NOT_MODIFIED, mime, "")
          res.addHeader("ETag", etag)
        } else {
          if (endAt < 0) {
            endAt = fileLen - 1
          }
          var newLen = endAt - startFrom + 1
          if (newLen < 0) {
            newLen = 0
          }

          val fis = FileInputStream(file)
          fis.skip(startFrom)

          res = newFixedLengthResponse(Response.Status.PARTIAL_CONTENT, mime, fis, newLen)
          res.addHeader("Accept-Ranges", "bytes")
          res.addHeader("Content-Length", "" + newLen)
          res.addHeader("Content-Range", "bytes $startFrom-$endAt/$fileLen")
          res.addHeader("ETag", etag)
        }
      } else {
        if (headerIfRangeMissingOrMatching && range != null && startFrom >= fileLen) {
          // return the size of the file
          // 4xx responses are not trumped by if-none-match
          res = SimpleWebServer.newFixedLengthResponse(
            Response.Status.RANGE_NOT_SATISFIABLE,
            MIME_PLAINTEXT,
            ""
          )
          res.addHeader("Content-Range", "bytes */$fileLen")
          res.addHeader("ETag", etag)
        } else if (range == null && headerIfNoneMatchPresentAndMatching) {
          // full-file-fetch request
          // would return entire file
          // respond with not-modified
          res = SimpleWebServer.newFixedLengthResponse(Response.Status.NOT_MODIFIED, mime, "")
          res.addHeader("ETag", etag)
        } else if (!headerIfRangeMissingOrMatching && headerIfNoneMatchPresentAndMatching) {
          // range request that doesn't match current etag
          // would return entire (different) file
          // respond with not-modified

          res = SimpleWebServer.newFixedLengthResponse(Response.Status.NOT_MODIFIED, mime, "")
          res.addHeader("ETag", etag)
        } else {
          // supply the file
          res = newFixedFileResponse(file, mime)
          res.addHeader("Content-Length", "" + fileLen)
          res.addHeader("ETag", etag)
        }
      }
    } catch (ioe: IOException) {
      res = getForbiddenResponse("Reading file failed.")
    }

    return res
  }


  @Throws(FileNotFoundException::class)
  private fun newFixedFileResponse(file: File, mime: String): Response {
    val res = newFixedLengthResponse(
      Response.Status.OK,
      mime,
      FileInputStream(file),
      file.length().toInt().toLong()
    )
    res.addHeader("Accept-Ranges", "bytes")
    return res
  }

  fun getForbiddenResponse(msg: String): Response {
    return newFixedLengthResponse(
      Response.Status.FORBIDDEN,
      "application/json",
      ResponseData<String>(code = 403, message = msg).toJsonString()
    )
  }

  fun getInternalErrorResponse(e: Exception): Response {
    return newFixedLengthResponse(
      Response.Status.INTERNAL_ERROR,
      "application/json",
      ResponseData<String>(code = 500, message = e.message ?: "Internal error").toJsonString()
    )
  }

  fun getNotFoundResponse(msg: String?): Response {
    return newFixedLengthResponse(
      Response.Status.NOT_FOUND,
      "application/json",
      ResponseData<String>(code = 404, message = msg ?: "Error 404, file not found.").toJsonString()
    )
  }

  fun getMethodNotAllowedResponse(msg: String?): Response {
    return newFixedLengthResponse(
      Response.Status.METHOD_NOT_ALLOWED,
      "application/json",
      ResponseData<String>(code = 405, message = msg ?: "Method Not Allowed").toJsonString()
    )
  }

  fun getBadRequestResponse(msg: String?): Response {
    return newFixedLengthResponse(
      Response.Status.BAD_REQUEST,
      "application/json",
      ResponseData<String>(code = 400, message = msg ?: "BAD_REQUEST", data = msg).toJsonString()
    )
  }
}