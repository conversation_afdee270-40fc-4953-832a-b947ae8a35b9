<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSKit</title>
    <script src="clear_launcher.js"></script>
</head>
<body>
<h1>Browser Test</h1>
<button type="button" id="sendBrowserMessage" onclick="sendBrowserMessage('text', 'hello!')">sendBrowserMessage</button>
<button type="button" id="sendBroadcast" onclick="sendBroadcast('cn.cleartv.launcher.action.SEND_WEB_MESSAGE', '{}')">sendBroadcast</button>
<button type="button" id="startActivity" onclick="startActivity('com.android.settings', null)">startActivity</button>
<button type="button" id="getStoreData" onclick="getData('browser_test', 'key')">getStoreData</button>
<button type="button" id="putStoreData" onclick="putData('browser_test', 'key', 'value')">putStoreData</button>
<button type="button" id="clearDataStore" onclick="clearData('browser_test')">clearStoreData</button>
<button type="button" id="getDeviceInfo" onclick="deviceInfo()">getDeviceInfo</button>

<div id="messageContainer">
    <!-- Messages will be displayed here -->
</div>
<script>
function onBrowserMessage(message) {
    console.log("onBrowserMessage: " + message);

    const now = new Date();
    const timeString = now.toLocaleTimeString();
    const formattedMessage = `${timeString}: ${message}`;
    const messageContainer = document.getElementById("messageContainer");
    const messageElement = document.createElement("pre");
    messageElement.textContent = formattedMessage;
    messageContainer.appendChild(messageElement);
}

function getData(storeName, key) {
    const value = getStoreData(storeName, key);
    const now = new Date();
    const timeString = now.toLocaleTimeString();
    const formattedMessage = `${timeString}: getData from ${storeName} ${key}=${value}`;
    const messageContainer = document.getElementById("messageContainer");
    const messageElement = document.createElement("pre");
    messageElement.textContent = formattedMessage;
    messageContainer.appendChild(messageElement);
}

function putData(storeName, key, value) {
    putStoreData(storeName, key, value);
    const now = new Date();
    const timeString = now.toLocaleTimeString();
    const formattedMessage = `${timeString}: putData from ${storeName} ${key}=${value}`;
    const messageContainer = document.getElementById("messageContainer");
    const messageElement = document.createElement("pre");
    messageElement.textContent = formattedMessage;
    messageContainer.appendChild(messageElement);
}

function clearData(storeName) {
    clearStoreData(storeName);
    const now = new Date();
    const timeString = now.toLocaleTimeString();
    const formattedMessage = `${timeString}: clearData from ${storeName}`;
    const messageContainer = document.getElementById("messageContainer");
    const messageElement = document.createElement("pre");
    messageElement.textContent = formattedMessage;
    messageContainer.appendChild(messageElement);
}

function deviceInfo() {
    const message = getDeviceInfo();
    const now = new Date();
    const timeString = now.toLocaleTimeString(); // Format the time as HH:MM:SS
    const formattedMessage = `${timeString}: ${message}`;
    const messageContainer = document.getElementById("messageContainer");
    const messageElement = document.createElement("pre");
    messageElement.textContent = formattedMessage;
    messageContainer.appendChild(messageElement);
}
</script>
</body>
</html>