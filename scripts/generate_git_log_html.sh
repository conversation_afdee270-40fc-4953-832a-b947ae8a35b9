#!/bin/bash

# Default output file name
DEFAULT_OUTPUT_FILE="git_log.html"

# Check if an output file name is provided as an argument
if [ -n "$1" ]; then
  OUTPUT_FILE="$1"
else
  OUTPUT_FILE="$DEFAULT_OUTPUT_FILE"
fi

git log --pretty=format:'<div class="commit">
  <div class="commit-header">%s - %an (%ad)</div>
  <div class="commit-body">%b</div>
</div>' > "$OUTPUT_FILE"

echo "
<!DOCTYPE html>
<html>
<head>
  <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">
  <title>Git Log</title>
  <style>
    body {
      font-family: sans-serif;
    }
    .commit {
      border: 1px solid #ccc;
      margin-bottom: 10px;
      padding: 10px;
    }
    .commit-header {
      font-weight: bold;
    }
    .commit-body {
      white-space: pre-wrap;
    }
  </style>
</head>
<body>
  <h1>Git Log</h1>
  $(cat "$OUTPUT_FILE")
</body>
</html>
" > "$OUTPUT_FILE"

echo "Git log HTML generated: $OUTPUT_FILE"