import java.nio.file.Files

rootProject.name = 'shadow'

copyLocalPropertiesForIncludeBuilds()
includeBuild 'projects/sdk/coding'
includeBuild 'projects/sdk/core'
includeBuild 'projects/sdk/dynamic'

includeTest()
includeSample()
includeSampleDynamicApk()

def includeTest() {
    include 'test-dynamic-host',
            'test-dynamic-manager',
            'test-dynamic-loader',
            'test-dynamic-runtime',
            'common-jar-settings-test',
            'constant',
            'custom-view',
            'plugin-use-host-code-lib',
            'test-manager',
            'general-cases-lib',
            'test-plugin-general-cases',
            'general-cases-app',
            'androidx-cases-lib',
            'androidx-cases-app',
            'test-plugin-androidx-cases',
            'plugin-service-for-host',
            'test-none-dynamic-host'
    project(':test-dynamic-host').projectDir = file('projects/test/dynamic/host/test-dynamic-host')
    project(':test-dynamic-manager').projectDir = file('projects/test/dynamic/manager/test-dynamic-manager')
    project(':test-dynamic-loader').projectDir = file('projects/test/dynamic/plugin/test-dynamic-loader')
    project(':test-dynamic-runtime').projectDir = file('projects/test/dynamic/plugin/test-dynamic-runtime')
    project(':common-jar-settings-test').projectDir = file('projects/test/common-jar-settings-test')
    project(':constant').projectDir = file('projects/test/lib/constant')
    project(':custom-view').projectDir = file('projects/test/lib/custom-view')
    project(':plugin-use-host-code-lib').projectDir = file('projects/test/lib/plugin-use-host-code-lib')
    project(':test-manager').projectDir = file('projects/test/lib/test-manager')
    project(':general-cases-lib').projectDir = file('projects/test/plugin/general-cases/general-cases-lib')
    project(':androidx-cases-lib').projectDir = file('projects/test/plugin/androidx-cases/androidx-cases-lib')
    project(':androidx-cases-app').projectDir = file('projects/test/plugin/androidx-cases/androidx-cases-app')
    project(':test-plugin-androidx-cases').projectDir = file('projects/test/plugin/androidx-cases/test-plugin-androidx-cases')
    project(':test-plugin-general-cases').projectDir = file('projects/test/plugin/general-cases/test-plugin-general-cases')
    project(':general-cases-app').projectDir = file('projects/test/plugin/general-cases/general-cases-app')
    project(':plugin-service-for-host').projectDir = file('projects/test/plugin/particular-cases/plugin-service-for-host')
    project(':test-none-dynamic-host').projectDir = file('projects/test/none-dynamic/host/test-none-dynamic-host')
}

def includeSample() {
    include 'sample-constant',
            'sample-host',
            'sample-host-lib',
            'sample-manager',
            'sample-loader',
            'sample-runtime',
            'pinnedheaderexpandablelistview',
            'slidingmenu',
            'sample-base-lib',
            'sample-base',
            'sample-app'
    project(':sample-constant').projectDir = file('projects/sample/source/sample-constant')
    project(':sample-host').projectDir = file('projects/sample/source/sample-host')
    project(':sample-host-lib').projectDir = file('projects/sample/source/sample-host-lib')
    project(':sample-manager').projectDir = file('projects/sample/source/sample-manager')
    project(':sample-loader').projectDir = file('projects/sample/source/sample-plugin/sample-loader')
    project(':sample-runtime').projectDir = file('projects/sample/source/sample-plugin/sample-runtime')
    project(':pinnedheaderexpandablelistview').projectDir = file('projects/sample/source/sample-plugin/third-party/pinnedheaderexpandablelistview')
    project(':slidingmenu').projectDir = file('projects/sample/source/sample-plugin/third-party/slidingmenu')
    project(':sample-base-lib').projectDir = file('projects/sample/source/sample-plugin/sample-base-lib')
    project(':sample-base').projectDir = file('projects/sample/source/sample-plugin/sample-base')
    project(':sample-app').projectDir = file('projects/sample/source/sample-plugin/sample-app')
}

def includeSampleDynamicApk() {
    include 'sample-hello-api',
            'sample-hello-api-holder',
            'sample-hello-apk',
            'sample-hello-host'
    project(':sample-hello-api').projectDir = file('projects/sample/dynamic-apk/sample-hello-api')
    project(':sample-hello-api-holder').projectDir = file('projects/sample/dynamic-apk/sample-hello-api-holder')
    project(':sample-hello-apk').projectDir = file('projects/sample/dynamic-apk/sample-hello-apk')
    project(':sample-hello-host').projectDir = file('projects/sample/dynamic-apk/sample-hello-host')
}

/**
 * Android Studio当前不会为IncludeBuild创建包含sdk.dir的local.properties
 * 使得没有ANDROID_HOME或等效环境变量时仅依赖根目录的local.properties无法编译IncludeBuild。
 * 为了使此含有IncludeBuild的项目和其他不含有IncludeBuild的普通Android工程一样可以在
 * 只有根目录的local.properties情况下正常编译，用此任务复制local.properties。
 */
def copyLocalPropertiesForIncludeBuilds() {
    def rootFile = file('local.properties')
    if (rootFile.exists()) {
        ['coding', 'core', 'dynamic'].forEach {
            def includeBuildFile = file("projects/sdk/${it}/local.properties")
            if (!includeBuildFile.exists()) {
                Files.copy(rootFile.toPath(), includeBuildFile.toPath())
            }
        }
    }
}
