<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@color/black"
    xmlns:app="http://schemas.android.com/apk/res-auto">



    <cn.cleartv.rtc.ClearRenderView
        android:id="@+id/render_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="2dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>


</androidx.constraintlayout.widget.ConstraintLayout>