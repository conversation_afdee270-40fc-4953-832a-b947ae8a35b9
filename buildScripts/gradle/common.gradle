def gitShortRev() {
    def gitCommit = ""
    def proc = "git rev-parse --short HEAD".execute()
    proc.in.eachLine { line -> gitCommit = line }
    proc.err.eachLine { line -> println line }
    proc.waitFor()
    return gitCommit
}

allprojects {
//    signingConfigs {
//        release {
//            if (System.getenv("KEYSTORE_PATH") != null) {
//                storeFile rootProject.file(System.getenv("KEYSTORE_PATH"))
//                keyAlias System.getenv("KEY_ALIAS")
//                keyPassword System.getenv("KEY_PASSWORD")
//                storePassword System.getenv("STORE_PASSWORD")
//            } else {
//                Properties properties = new Properties()
//                properties.load(project.rootProject.file('local.properties').newDataInputStream())
//                storeFile properties.getProperty('storeFile')
//                keyAlias properties.getProperty('keyAlias')
//                keyPassword properties.getProperty('keyPassword')
//                storePassword properties.getProperty('storePassword')
//            }
//        }
//    }
}
