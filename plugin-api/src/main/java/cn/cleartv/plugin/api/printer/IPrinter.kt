package cn.cleartv.plugin.api.printer

import android.graphics.Bitmap

interface IPrinter {

  fun open(): Boolean
  fun close()
  fun status(): PrinterStatus
  fun restart()
  fun launchTestApp()
  fun printBitmap(
    bitmap: Bitmap,
    paperWidth: Int,
    paperHeight: Int,
    posX: Int,
    posY: Int,
    @CutMode cutMode: Int
  )

  fun getSupportCutMode(): IntArray
  fun setStopPosition(position: Int)
  fun setPrintSpeed(speed: Int)
  fun setPrintDarkLevel(level: Int)
  fun setPaperType(@PaperType type: Int)
  fun getSupportPaperType(): IntArray
  fun useThermalTransfer(use: Boolean)

}