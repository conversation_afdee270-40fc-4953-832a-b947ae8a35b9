plugins {
  alias(libs.plugins.android.application) apply false
  alias(libs.plugins.kotlin.android) apply false
  alias(libs.plugins.dokka) apply false
  alias(libs.plugins.android.library) apply false
  alias(libs.plugins.shadow.plugin) apply false
  alias(libs.plugins.aar.to.jar) apply false
}

buildscript {
  repositories {
    google()
    mavenCentral()
    mavenLocal()
  }
  dependencies {
    classpath (libs.aar.to.jar.plugin)
    classpath (libs.common.jar.settings)
  }
}

apply("buildScripts/gradle/common.gradle")