pluginManagement {
  repositories {
    mavenLocal()
    google {
      content {
        includeGroupByRegex("com\\.android.*")
        includeGroupByRegex("com\\.google.*")
        includeGroupByRegex("androidx.*")
      }
    }
    mavenCentral()
    gradlePluginPortal()
  }
}
dependencyResolutionManagement {
  repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
  repositories {
    mavenLocal()
    google()
    mavenCentral()
    maven{ setUrl("https://maven.aliyun.com/repository/jcenter") }
    maven{ setUrl("https://jitpack.io") }
  }
}


includeBuild("Shadow/projects/sdk/coding")
includeBuild("Shadow/projects/sdk/core")
includeBuild("Shadow/projects/sdk/dynamic")

rootProject.name = "Launcher"
include(":app")
include(":plugin-constant")
include(":plugin-api")
include(":plugin-api-holder")
include(":plugin-apk-cardreader")
include(":plugin-manager")
include(":plugin-loader")
include(":plugin-app-lib")
include(":plugin-runtime")
include(":plugin-apk-rtc")

