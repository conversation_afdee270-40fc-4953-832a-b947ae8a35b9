variables:
  GRADLE_OPTS: "-Dorg.gradle.daemon=false" # 禁用 gradle 守护进程
  server_ssh: gitci@***********
  ssh_password: 123456
  upload_path: /data/clearrtc_v3_gitlab_runner/frontend/client/android
  GIT_SUBMODULE_STRATEGY: recursive


before_script:
  - chmod +x ./gradlew

stages:
  - build
  - deploy


build:
  stage: build
  image: mingc/android-build-box:cleartv 
  only:
    - master
  script:
    - pwd
    - ls -l
    - echo $KEYSTORE_PATH
    - ./gradlew :meeting:aR
  cache:
    key: build-cache
    paths:
      - meeting/build/outputs/apk/
  #artifacts:
  #  paths:
  #    - meeting/build/outputs/apk/release/
  #  expire_in: 2 week
  tags:
    - android

deploy:
  stage: deploy
  image: ictu/sshpass:latest
  only:
    - master
  cache:
    key: build-cache
    paths:
      - meeting/build/outputs/apk/
  script:
    - pwd
    - ls -l
    - find ./meeting/build/outputs/apk -name "*.apk"
    - find ./meeting/build/outputs/apk -name "*.apk" -exec cp {} ./ \;
    - ./meeting/upgrade_info.sh > upgrade.json
    - sshpass -p $ssh_password scp -o StrictHostKeyChecking=no ./meeting/build/outputs/apk/default/release/*.apk $server_ssh:$upload_path/
    - sshpass -p $ssh_password scp -o StrictHostKeyChecking=no upgrade.json $server_ssh:$upload_path/
  tags:
    - android
  artifacts:
    name: "Clearrtc-Android-$CI_COMMIT_SHORT_SHA"
    paths:
      - ./upgrade.json
      - ./*.apk
  