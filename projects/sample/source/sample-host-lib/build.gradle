apply plugin: 'com.android.library'
apply plugin: 'com.tencent.shadow.internal.aar-to-jar'

android {
    compileSdkVersion project.COMPILE_SDK_VERSION


    defaultConfig {
        minSdkVersion project.MIN_SDK_VERSION
        targetSdkVersion project.TARGET_SDK_VERSION
        versionCode project.VERSION_CODE
        versionName project.VERSION_NAME

        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"

    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            consumerProguardFiles 'sample-host-lib.pro'
        }
    }

}
