apply plugin: 'com.android.library'

android {
    compileSdkVersion project.COMPILE_SDK_VERSION

    defaultConfig {
        minSdkVersion project.MIN_SDK_VERSION
        targetSdkVersion project.TARGET_SDK_VERSION
        versionCode project.VERSION_CODE
        versionName project.VERSION_NAME
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            consumerProguardFiles 'proguard-rules.pro'

            signingConfig signingConfigs.create("release")
            signingConfig.initWith(buildTypes.debug.signingConfig)
        }
    }

    lintOptions {
        abortOnError false
    }
}

dependencies {
    implementation project(":slidingmenu")
    implementation project(":pinnedheaderexpandablelistview")
    implementation "com.android.support:support-annotations:$android_support_annotations_version"
    api "com.android.support:support-v4:$android_support_version"
    api "com.android.support:appcompat-v7:$android_support_version"
}
