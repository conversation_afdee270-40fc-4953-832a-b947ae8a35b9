<?xml version="1.0" encoding="utf-8"?><!--
  ~ <PERSON><PERSON> is pleased to support the open source community by making Tencent Shadow available.
  ~ Copyright (C) 2019 THL A29 Limited, a Tencent company.  All rights reserved.
  ~
  ~ Licensed under the BSD 3-Clause License (the "License"); you may not use
  ~ this file except in compliance with the License. You may obtain a copy of
  ~ the License at
  ~
  ~     https://opensource.org/licenses/BSD-3-Clause
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/holo_green_light"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="30dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/case_name"
            android:layout_width="200dp"
            android:layout_height="60dp"
            android:background="@android:color/white"
            android:gravity="left|center_vertical"
            android:paddingLeft="6dp"
            android:textColor="@android:color/black"
            android:textSize="16sp"
            android:text="Shadow主测试用例集合" />

        <Button
            android:id="@+id/start_case"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginLeft="40dp"
            android:background="@android:color/holo_orange_dark"
            android:visibility="gone"
            android:textColor="@android:color/white"
            tools:visibility="visible"
            android:text="启动" />

    </LinearLayout>

    <TextView
        android:id="@+id/case_summary"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="30dp"
        android:layout_marginRight="16dp"
        android:layout_weight="1"
        android:background="@android:color/white"
        android:gravity="left"
        android:padding="10dp"
        android:text="这是一个插件框架的测试用例，您可以测试代码在独立安装的环境和插件的环境运行的情况。
           \n\n左滑开始选择测试用例开始测试。"
        android:textColor="@android:color/black"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/environment"
        android:layout_width="160dp"
        android:layout_height="40dp"
        android:layout_gravity="right"
        android:layout_marginTop="20dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="20dp"
        android:background="@android:color/holo_red_light"
        android:gravity="left|center_vertical"
        android:paddingLeft="6dp"
        android:textColor="@android:color/white"
        android:textSize="16sp"
        tools:text="测试用例" />

</LinearLayout>