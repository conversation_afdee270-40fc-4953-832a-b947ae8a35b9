<!--
  ~ <PERSON><PERSON> is pleased to support the open source community by making Tencent Shadow available.
  ~ Copyright (C) 2019 THL A29 Limited, a Tencent company.  All rights reserved.
  ~
  ~ Licensed under the BSD 3-Clause License (the "License"); you may not use
  ~ this file except in compliance with the License. You may obtain a copy of
  ~ the License at
  ~
  ~     https://opensource.org/licenses/BSD-3-Clause
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->

<com.ryg.expandable.ui.StickyLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/sticky_layout"
    android:layout_width="0dp"
    android:layout_height="match_parent"
    android:layout_marginTop="0dp"
    android:layout_weight="1"
    android:background="@android:color/white"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/sticky_header"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:gravity="center"
        android:visibility="gone"
        android:orientation="vertical" />

    <LinearLayout
        android:id="@+id/sticky_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.ryg.expandable.ui.PinnedHeaderExpandableListView
            android:id="@+id/expandablelist"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:cacheColorHint="@null"
            android:childDivider="@drawable/child_bg"
            android:childIndicatorLeft="0dp"
            android:divider="@android:color/darker_gray"
            android:dividerHeight="1dp"
            android:groupIndicator="@null"
            android:scrollbarAlwaysDrawHorizontalTrack="false" />
    </LinearLayout>

</com.ryg.expandable.ui.StickyLayout>