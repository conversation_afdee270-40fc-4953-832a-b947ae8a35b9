<resources>

    <declare-styleable name="SlidingMenu">
        <attr name="mode">
            <enum name="left" value="0" />
            <enum name="right" value="1" />
        </attr>
        <attr name="viewAbove" format="reference" />
        <attr name="viewBehind" format="reference" />
        <attr name="behindOffset" format="dimension" />
        <attr name="behindWidth" format="dimension" />
        <attr name="behindScrollScale" format="float" />
        <attr name="touchModeAbove">
            <enum name="margin" value="0" />
            <enum name="fullscreen" value="1" />
            <enum name="none" value="2" />
        </attr>
        <attr name="touchModeBehind">
            <enum name="margin" value="0" />
            <enum name="fullscreen" value="1" />
            <enum name="none" value="2" />
        </attr>
        <attr name="shadowDrawable" format="reference" />
        <attr name="shadowWidth" format="dimension" />
        <attr name="fadeEnabled" format="boolean" />
        <attr name="fadeDegree" format="float" />
        <attr name="selectorEnabled" format="boolean" />
        <attr name="selectorDrawable" format="reference" />
    </declare-styleable>

</resources>