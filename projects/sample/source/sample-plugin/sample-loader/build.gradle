apply plugin: 'com.android.application'

android {
    compileSdkVersion project.COMPILE_SDK_VERSION
    defaultConfig {
        applicationId project.SAMPLE_HOST_APP_APPLICATION_ID
        minSdkVersion project.MIN_SDK_VERSION
        targetSdkVersion project.TARGET_SDK_VERSION
        versionCode project.VERSION_CODE
        versionName project.VERSION_NAME
    }
    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'

            signingConfig signingConfigs.create("release")
            signingConfig.initWith(buildTypes.debug.signingConfig)
        }
    }
}

dependencies {
    implementation 'com.tencent.shadow.core:loader'
    implementation 'com.tencent.shadow.dynamic:dynamic-loader'
    implementation 'com.tencent.shadow.dynamic:dynamic-loader-impl'
    implementation project(':sample-constant')

    compileOnly 'com.tencent.shadow.core:runtime'
    compileOnly 'com.tencent.shadow.core:activity-container'
    compileOnly 'com.tencent.shadow.core:common'
    //下面这行依赖是为了防止在proguard的时候找不到LoaderFactory接口
    compileOnly 'com.tencent.shadow.dynamic:dynamic-host'

    compileOnly files("${project(":sample-host-lib").getBuildDir()}/outputs/jar/sample-host-lib-debug.jar")
}

preBuild.dependsOn(":sample-host-lib:jarDebugPackage")
