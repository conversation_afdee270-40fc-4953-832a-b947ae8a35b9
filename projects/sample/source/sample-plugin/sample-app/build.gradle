buildscript {
    repositories {
        if (!System.getenv().contains<PERSON>ey("DISABLE_TENCENT_MAVEN_MIRROR")) {
            maven { url 'https://mirrors.tencent.com/nexus/repository/maven-public/' }
        } else {
            google()
            mavenCentral()
        }
    }

    dependencies {
        classpath 'com.tencent.shadow.core:runtime'
        classpath 'com.tencent.shadow.core:activity-container'
        classpath 'com.tencent.shadow.core:gradle-plugin'
        classpath "org.javassist:javassist:$javassist_version"
    }
}

apply plugin: 'com.android.application'
apply plugin: 'com.tencent.shadow.plugin'

android {
    compileSdkVersion project.COMPILE_SDK_VERSION

    defaultConfig {
        applicationId 'com.tencent.shadow.sample.plugin.app'
        minSdkVersion project.MIN_SDK_VERSION
        targetSdkVersion project.TARGET_SDK_VERSION
        versionCode project.VERSION_CODE
        versionName project.VERSION_NAME
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'

            signingConfig signingConfigs.create("release")
            signingConfig.initWith(buildTypes.debug.signingConfig)
        }
    }

    // 将插件applicationId设置为和宿主相同
    productFlavors {
        plugin {
            applicationId project.SAMPLE_HOST_APP_APPLICATION_ID
        }
    }

    lintOptions {
        abortOnError false
    }

    // 将插件的资源ID分区改为和宿主0x7F不同的值
    aaptOptions {
        additionalParameters "--package-id", "0x7E", "--allow-reserved-package-id"
    }
}

dependencies {
    //注意sample-host-lib要用compileOnly编译而不打包在插件中。在packagePlugin任务中配置hostWhiteList允许插件访问宿主的类。
    pluginCompileOnly project(":sample-host-lib")
    normalImplementation project(":sample-host-lib")

    pluginCompileOnly project(":sample-base-lib")
    normalImplementation project(":sample-base-lib")

    //Shadow Transform后业务代码会有一部分实际引用runtime中的类
    //如果不以compileOnly方式依赖，会导致其他Transform或者Proguard找不到这些类
    pluginCompileOnly 'com.tencent.shadow.core:runtime'
}

preBuild.dependsOn(":sample-host-lib:jarDebugPackage")


def createDuplicateApkTask(buildType) {
    def apkDir = file("${getBuildDir()}/outputs/apk/plugin/$buildType")

    return tasks.create("duplicatePlugin${buildType.capitalize()}ApkTask", Copy) {
        group = 'build'
        description = "复制一个sample-app-plugin-${buildType}.apk用于测试目的"
        from(apkDir) {
            include("sample-app-plugin-${buildType}.apk")
            rename { "sample-app-plugin-${buildType}2.apk" }
        }
        into(apkDir)

    }.dependsOn(":sample-app:assemblePlugin${buildType.capitalize()}")
}

tasks.whenTaskAdded { task ->
    if (task.name == "assemblePluginDebug") {
        def createTask = createDuplicateApkTask('debug')
        task.finalizedBy(createTask)
    }
    if (task.name == "assemblePluginRelease") {
        def createTask = createDuplicateApkTask('release')
        task.finalizedBy(createTask)
    }
}


shadow {
    transform {
//        useHostContext = ['abc']
    }

    packagePlugin {
        pluginTypes {
            debug {
                loaderApkConfig = new Tuple2('sample-loader-debug.apk', ':sample-loader:assembleDebug')
                runtimeApkConfig = new Tuple2('sample-runtime-debug.apk', ':sample-runtime:assembleDebug')
                pluginApks {
                    pluginApk1 {
                        businessName = 'sample-plugin-app'
                        partKey = 'sample-plugin-app'
                        buildTask = ':sample-app:assemblePluginDebug'
                        apkPath = 'projects/sample/source/sample-plugin/sample-app/build/outputs/apk/plugin/debug/sample-app-plugin-debug.apk'
                        hostWhiteList = ["com.tencent.shadow.sample.host.lib"]
                        dependsOn = ['sample-base']
                    }
                    pluginApk2 {
                        businessName = 'sample-plugin-app2'
                        partKey = 'sample-plugin-app2'
                        buildTask = ':sample-app:assemblePluginDebug'
                        apkPath = 'projects/sample/source/sample-plugin/sample-app/build/outputs/apk/plugin/debug/sample-app-plugin-debug2.apk'
                        hostWhiteList = ["com.tencent.shadow.sample.host.lib"]
                        dependsOn = ['sample-base']
                    }
                    sampleBase {
                        businessName = 'sample-plugin-app'
                        partKey = 'sample-base'
                        buildTask = ':sample-base:assemblePluginDebug'
                        apkPath = 'projects/sample/source/sample-plugin/sample-base/build/outputs/apk/plugin/debug/sample-base-plugin-debug.apk'
                        hostWhiteList = ["com.tencent.shadow.sample.host.lib"]
                    }
                }
            }

            release {
                loaderApkConfig = new Tuple2('sample-loader-release.apk', ':sample-loader:assembleRelease')
                runtimeApkConfig = new Tuple2('sample-runtime-release.apk', ':sample-runtime:assembleRelease')
                pluginApks {
                    pluginApk1 {
                        businessName = 'sample-plugin-app'
                        partKey = 'sample-plugin-app'
                        buildTask = ':sample-app:assemblePluginRelease'
                        apkPath = 'projects/sample/source/sample-plugin/sample-app/build/outputs/apk/plugin/release/sample-app-plugin-release.apk'
                        hostWhiteList = ["com.tencent.shadow.sample.host.lib"]
                        dependsOn = ['sample-base']
                    }
                    pluginApk2 {
                        businessName = 'sample-plugin-app2'
                        partKey = 'sample-plugin-app2'
                        buildTask = ':sample-app:assemblePluginRelease'
                        apkPath = 'projects/sample/source/sample-plugin/sample-app/build/outputs/apk/plugin/release/sample-app-plugin-release2.apk'
                        hostWhiteList = ["com.tencent.shadow.sample.host.lib"]
                        dependsOn = ['sample-base']
                    }
                    sampleBase {
                        businessName = 'sample-plugin-app'
                        partKey = 'sample-base'
                        buildTask = ':sample-base:assemblePluginRelease'
                        apkPath = 'projects/sample/source/sample-plugin/sample-base/build/outputs/apk/plugin/release/sample-base-plugin-release.apk'
                        hostWhiteList = ["com.tencent.shadow.sample.host.lib"]
                    }
                }
            }
        }

        loaderApkProjectPath = 'projects/sample/source/sample-plugin/sample-loader'
        runtimeApkProjectPath = 'projects/sample/source/sample-plugin/sample-runtime'

        archiveSuffix = System.getenv("PluginSuffix") ?: ""
        archivePrefix = 'plugin'
        destinationDir = "${getRootProject().getBuildDir()}"

        version = 4
        compactVersion = [1, 2, 3]
        uuidNickName = "1.1.5"
    }
}
