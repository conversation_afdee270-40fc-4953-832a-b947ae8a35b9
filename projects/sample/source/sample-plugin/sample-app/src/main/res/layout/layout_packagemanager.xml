<?xml version="1.0" encoding="utf-8"?><!--
  ~ <PERSON><PERSON> is pleased to support the open source community by making Tencent Shadow available.
  ~ Copyright (C) 2019 THL A29 Limited, a Tencent company.  All rights reserved.
  ~
  ~ Licensed under the BSD 3-Clause License (the "License"); you may not use
  ~ this file except in compliance with the License. You may obtain a copy of
  ~ the License at
  ~
  ~     https://opensource.org/licenses/BSD-3-Clause
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingLeft="20dp"
    android:orientation="vertical">


    <Button
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:layout_marginTop="8dp"
        android:onClick="getApplicationInfo"
        android:text="getApplicationInfo"
        android:layout_gravity="top" />


    <Button
        android:id="@+id/button1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:layout_marginTop="8dp"
        android:onClick="getActivityInfo"
        android:text="getActivityInfo"
        android:layout_gravity="top" />


    <Button
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="top"
        android:layout_marginBottom="8dp"
        android:layout_marginTop="8dp"
        android:onClick="getPackageInfo"
        android:text="getPackageInfo" />


    <TextView
        android:id="@+id/text"
        android:layout_marginTop="20dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@android:color/white"

        />


</LinearLayout>
