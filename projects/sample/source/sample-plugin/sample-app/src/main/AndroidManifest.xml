<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.tencent.shadow.sample.plugin.app.lib">

    <uses-feature android:glEsVersion="0x00020000" />

    <application
        android:name="com.tencent.shadow.sample.plugin.app.lib.UseCaseApplication"
        android:icon="@android:drawable/sym_def_app_icon"
        android:theme="@android:style/Theme.NoTitleBar"
        android:label="@string/app_name">

        <meta-data
            android:name="test_meta"
            android:value="test_value" />


        <activity android:name="com.tencent.shadow.sample.plugin.app.lib.usecases.activity.TestActivityReCreate" />
        <activity android:name="com.tencent.shadow.sample.plugin.app.lib.usecases.activity.TestActivitySetTheme" />
        <activity android:name="com.tencent.shadow.sample.plugin.app.lib.usecases.activity.TestActivityOptionMenu" />

        <activity android:name="com.tencent.shadow.sample.plugin.app.lib.usecases.activity.TestActivityOnCreate" />

        <activity
            android:name="com.tencent.shadow.sample.plugin.app.lib.usecases.activity.TestActivityOrientation"
            android:configChanges="orientation|screenSize|fontScale"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name="com.tencent.shadow.sample.plugin.app.lib.usecases.activity.TestActivityWindowSoftMode"
            android:windowSoftInputMode="stateVisible" />

        <activity android:name="com.tencent.shadow.sample.plugin.app.lib.usecases.provider.TestDBContentProviderActivity" />

        <activity android:name="com.tencent.shadow.sample.plugin.app.lib.usecases.activity.TestActivityReCreateBySystem" />

        <activity android:name="com.tencent.shadow.sample.plugin.app.lib.usecases.receiver.TestReceiverActivity" />

        <activity android:name="com.tencent.shadow.sample.plugin.app.lib.usecases.receiver.TestDynamicReceiverActivity" />

        <activity android:name="com.tencent.shadow.sample.plugin.app.lib.usecases.fragment.TestDynamicFragmentActivity" />

        <activity android:name="com.tencent.shadow.sample.plugin.app.lib.usecases.fragment.TestXmlFragmentActivity" />

        <activity android:name="com.tencent.shadow.sample.plugin.app.lib.usecases.dialog.TestDialogActivity" />

        <activity android:name="com.tencent.shadow.sample.plugin.app.lib.usecases.packagemanager.TestPackageManagerActivity" />

        <receiver android:name="com.tencent.shadow.sample.plugin.app.lib.usecases.receiver.MyReceiver">
            <intent-filter>
                <action android:name="com.tencent.test.action" />
            </intent-filter>
        </receiver>

        <activity android:name="com.tencent.shadow.sample.plugin.app.lib.usecases.provider.TestFileProviderActivity" />

        <activity android:name="com.tencent.shadow.sample.plugin.app.lib.usecases.application.TestApplicationActivity" />

        <activity android:name="com.tencent.shadow.sample.plugin.app.lib.usecases.context.ActivityContextSubDirTestActivity" />

        <activity android:name="com.tencent.shadow.sample.plugin.app.lib.usecases.context.ApplicationContextSubDirTestActivity" />
        <activity android:name=".usecases.host_communication.PluginUseHostClassActivity" />
        <activity android:name=".usecases.webview.WebViewActivity" />
        <activity android:name=".usecases.fragment.TestDialogFragmentActivity" />

        <provider
            android:authorities="${applicationId}.provider.test"
            android:name="com.tencent.shadow.sample.plugin.app.lib.usecases.provider.TestProvider" />

        <service android:name=".usecases.service.HostAddPluginViewService" />
    </application>

</manifest>