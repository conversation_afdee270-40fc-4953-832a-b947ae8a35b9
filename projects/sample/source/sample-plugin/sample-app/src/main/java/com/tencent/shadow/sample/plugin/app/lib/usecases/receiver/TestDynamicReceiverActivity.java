/*
 * <PERSON><PERSON> is pleased to support the open source community by making Tencent Shadow available.
 * Copyright (C) 2019 THL A29 Limited, a Tencent company.  All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 *     https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.tencent.shadow.sample.plugin.app.lib.usecases.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.view.View;
import android.widget.Button;

import com.tencent.shadow.sample.plugin.app.lib.R;
import com.tencent.shadow.sample.plugin.app.lib.gallery.BaseActivity;
import com.tencent.shadow.sample.plugin.app.lib.gallery.cases.entity.UseCase;
import com.tencent.shadow.sample.plugin.app.lib.gallery.util.ToastUtil;

public class TestDynamicReceiverActivity extends BaseActivity {

    public static class Case extends UseCase {
        @Override
        public String getName() {
            return "动态广播测试";
        }

        @Override
        public String getSummary() {
            return "测试动态广播的发送和接收是否工作正常";
        }

        @Override
        public Class getPageClass() {
            return TestDynamicReceiverActivity.class;
        }
    }

    private final static String INTENT_ACTION = "com.tencent.test.action.DYNAMIC";

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.layout_receiver);
        Button button = findViewById(R.id.button);
        button.setText("测试动态广播发送");

        button.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(INTENT_ACTION);
                intent.putExtra("msg", "收到测试动态广播发送");
                sendBroadcast(intent);
            }
        });

        DynamicBroadcastReceiver dynamicBroadcastReceiver = new DynamicBroadcastReceiver();
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.TIRAMISU) {
            registerReceiver(dynamicBroadcastReceiver, new IntentFilter(INTENT_ACTION), Context.RECEIVER_EXPORTED);
        } else {
            registerReceiver(dynamicBroadcastReceiver, new IntentFilter(INTENT_ACTION));
        }
    }


    private class DynamicBroadcastReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            String msg = intent.getStringExtra("msg");
            ToastUtil.showToast(context, msg);
        }
    }

}
