apply plugin: 'com.android.application'

android {
    compileSdkVersion project.COMPILE_SDK_VERSION
    defaultConfig {
        applicationId project.SAMPLE_HOST_APP_APPLICATION_ID
        minSdkVersion project.MIN_SDK_VERSION
        targetSdkVersion project.TARGET_SDK_VERSION
        versionCode project.VERSION_CODE
        versionName project.VERSION_NAME
    }
    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'

            signingConfig signingConfigs.create("release")
            signingConfig.initWith(buildTypes.debug.signingConfig)
        }
    }
}

dependencies {
    implementation 'com.tencent.shadow.core:activity-container'
}

