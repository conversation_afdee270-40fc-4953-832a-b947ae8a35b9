buildscript {
    repositories {
        if (!System.getenv().contains<PERSON>ey("DISABLE_TENCENT_MAVEN_MIRROR")) {
            maven { url 'https://mirrors.tencent.com/nexus/repository/maven-public/' }
        } else {
            google()
            mavenCentral()
        }
    }

    dependencies {
        classpath 'com.tencent.shadow.core:runtime'
        classpath 'com.tencent.shadow.core:activity-container'
        classpath 'com.tencent.shadow.core:gradle-plugin'
        classpath "org.javassist:javassist:$javassist_version"
    }
}

apply plugin: 'com.android.application'
apply plugin: 'com.tencent.shadow.plugin'

android {
    compileSdkVersion project.COMPILE_SDK_VERSION

    defaultConfig {
        applicationId 'com.tencent.shadow.sample.plugin.lib.base'
        minSdkVersion project.MIN_SDK_VERSION
        targetSdkVersion project.TARGET_SDK_VERSION
        versionCode project.VERSION_CODE
        versionName project.VERSION_NAME
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'

            signingConfig signingConfigs.create("release")
            signingConfig.initWith(buildTypes.debug.signingConfig)
        }
    }

    // 将插件applicationId设置为和宿主相同
    productFlavors {
        plugin {
            applicationId project.SAMPLE_HOST_APP_APPLICATION_ID
        }
    }

    // 将插件的资源ID分区改为和宿主0x7F不同的值
    aaptOptions {
        additionalParameters "--package-id", "0x7E", "--allow-reserved-package-id"
    }

    lintOptions {
        abortOnError false
    }
}

dependencies {
    implementation project(":sample-base-lib")

    //Shadow Transform后业务代码会有一部分实际引用runtime中的类
    //如果不以compileOnly方式依赖，会导致其他Transform或者Proguard找不到这些类
    pluginCompileOnly 'com.tencent.shadow.core:runtime'
}
