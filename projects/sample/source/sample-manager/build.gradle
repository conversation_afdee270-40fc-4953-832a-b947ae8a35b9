apply plugin: 'com.android.application'

android {
    compileSdkVersion project.COMPILE_SDK_VERSION
    defaultConfig {
        applicationId project.SAMPLE_HOST_APP_APPLICATION_ID
        minSdkVersion project.MIN_SDK_VERSION
        targetSdkVersion project.TARGET_SDK_VERSION
        versionCode project.VERSION_CODE
        versionName project.VERSION_NAME
    }
    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'

            signingConfig signingConfigs.create("release")
            signingConfig.initWith(buildTypes.debug.signingConfig)
        }
    }
    lintOptions {
        abortOnError false
    }
}

dependencies {
    implementation 'com.tencent.shadow.dynamic:dynamic-manager'
    implementation 'com.tencent.shadow.core:manager'
    implementation 'com.tencent.shadow.dynamic:dynamic-loader'
    implementation project(':sample-constant')

    compileOnly 'com.tencent.shadow.core:common'
    compileOnly 'com.tencent.shadow.dynamic:dynamic-host'
}
