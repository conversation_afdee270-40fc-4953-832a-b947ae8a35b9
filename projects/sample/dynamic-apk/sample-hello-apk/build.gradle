apply plugin: 'com.android.application'

android {
    compileSdkVersion project.COMPILE_SDK_VERSION
    defaultConfig {
        //region hello.apk 演示
        applicationId 'com.tencent.shadow.sample.hello.host' // 必须保证和 host 的 applicationId 一致
        //endregion
        minSdkVersion project.MIN_SDK_VERSION
        targetSdkVersion project.TARGET_SDK_VERSION
        versionCode project.VERSION_CODE
        versionName project.VERSION_NAME
    }
    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'

            signingConfig signingConfigs.create("release")
            signingConfig.initWith(buildTypes.debug.signingConfig)
        }
    }
    lintOptions {
        abortOnError false
    }
}

dependencies {
    // 自定义接口在宿主内
    // hello.apk 不必要自己打包一份，只需要通过白名单访问宿主的接口定义，所以是 compileOnly
    compileOnly project(":sample-hello-api")
}
