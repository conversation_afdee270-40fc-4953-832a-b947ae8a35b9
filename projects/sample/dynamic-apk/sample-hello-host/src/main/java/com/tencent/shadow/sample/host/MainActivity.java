/*
 * <PERSON><PERSON> is pleased to support the open source community by making Tencent Shadow available.
 * Copyright (C) 2019 THL A29 Limited, a Tencent company.  All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 *     https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.tencent.shadow.sample.host;

import android.app.Activity;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.tencent.shadow.sample.api.hello.IHelloWorld;
import com.tencent.shadow.sample.host.api.HelloWorldApiHolder;

public class MainActivity extends Activity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setTheme(android.R.style.Theme_NoTitleBar);

        LinearLayout rootView = new LinearLayout(this);
        rootView.setOrientation(LinearLayout.VERTICAL);

        rootView.addView(createTextView("演示自定义 api 的动态化，宿主 api 的实现在 hello.apk 中", null));

        final TextView textView = createTextView("等待apk实现", null);
        rootView.addView(createButton("宿主自定义接口的动态化", new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                PluginHelper.getInstance().singlePool.execute(new Runnable() {
                    @Override
                    public void run() {
                        //hello.apk 里实现了 IHelloWorld
                        final IHelloWorld api = HelloWorldApiHolder.getHelloWorld(PluginHelper.getInstance().helloApkFile);
                        if (api == null) {
                            return;
                        }
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                api.sayHelloWorld(MainActivity.this, textView);
                            }
                        });
                    }
                });
            }
        }));
        rootView.addView(textView);

        setContentView(rootView);
    }

    public Button createButton(String title, View.OnClickListener listener) {
        Button button = new Button(this);
        button.setText(title);
        button.setOnClickListener(listener);
        return button;
    }

    public TextView createTextView(String title, View.OnClickListener listener) {
        TextView textView = new TextView(this);
        textView.setText(title);
        textView.setOnClickListener(listener);
        return textView;
    }
}
