/*
 * <PERSON><PERSON> is pleased to support the open source community by making Tencent Shadow available.
 * Copyright (C) 2019 THL A29 Limited, a Tencent company.  All rights reserved.
 *
 * Licensed under the BSD 3-Clause License (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 *     https://opensource.org/licenses/BSD-3-Clause
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.tencent.shadow.sample.api.hello;

import android.os.Bundle;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/9/6
 * @description 给接口 IHelloWorld 包装一层生命周期
 * 可参考 com.tencent.shadow.sample.apk.hello.DynamicHello 中管理该生命周期
 * @usage hello.apk 里可以感知加载的过程
 */
public interface IHelloWorldImpl extends IHelloWorld {

    void onCreate(Bundle bundle);

    void onSaveInstanceState(Bundle outState);

    void onDestroy();
}
